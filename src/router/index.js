import { createRouter, createWebHashHistory } from 'vue-router'
import AppLayout from '../layouts/AppLayout.vue'
import ActivationPage from '../views/ActivationPage.vue'
import LoadingPage from '../views/LoadingPage.vue'
import AccountManagePage from '../views/AccountManagePage.vue'
import MonitorPanel from '../components/MonitorPanel.vue'
import SettingsPanel from '../components/SettingsPanel.vue'
import LogPanel from '../components/LogPanel.vue'
import DebugPanel from '../components/DebugPanel.vue'
import { useAppStore } from '../stores/app'

// 基础路由配置
const routes = [
  {
    path: '/loading',
    name: 'Loading',
    component: LoadingPage,
    meta: {
      requiresAuth: false,
      allowedStates: ['loading', 'initializing']
    }
  },
  {
    path: '/activation',
    name: 'Activation',
    component: ActivationPage,
    meta: {
      requiresAuth: false,
      allowedStates: ['not_activated', 'activation_required']
    }
  },
  {
    path: '/',
    component: AppLayout,
    meta: {
      requiresAuth: true,
      allowedStates: ['activated', 'running']
    },
    children: [
      {
        path: '',
        name: 'monitor',
        component: MonitorPanel,
        meta: {
          requiresAuth: true,
          allowedStates: ['activated', 'running']
        }
      },
      {
        path: 'settings',
        name: 'settings',
        component: SettingsPanel,
        meta: {
          requiresAuth: true,
          allowedStates: ['activated', 'running']
        }
      },
      {
        path: 'logs',
        name: 'logs',
        component: LogPanel,
        meta: {
          requiresAuth: true,
          allowedStates: ['activated', 'running']
        }
      },
      {
        path: 'accounts',
        name: 'accounts',
        component: AccountManagePage,
        meta: {
          requiresAuth: true,
          allowedStates: ['activated', 'running']
        }
      },
      {
        path: 'debug',
        name: 'debug',
        component: DebugPanel,
        meta: {
          requiresAuth: true,
          allowedStates: ['activated', 'running']
        }
      }
    ]
  },
  // 捕获所有未匹配的路由，重定向到loading
  {
    path: '/:pathMatch(.*)*',
    redirect: '/loading'
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫 - 防止绕过激活限制
router.beforeEach((to, from, next) => {
  // 开发环境下跳过权限校验
  if (import.meta.env.DEV) {
    console.log('🔧 开发环境：跳过路由权限校验')
    next()
    return
  }

  const appStore = useAppStore()
  const currentState = appStore.appState
  const isActivated = appStore.isActivated

  console.log('🛡️ 路由守卫检查:', {
    to: to.path,
    from: from.path,
    currentState,
    isActivated,
    toMeta: to.meta
  })

  // 优先处理激活状态相关的路由
  if (!isActivated) {
    // 如果未激活，只允许访问loading和activation页面
    if (to.path !== '/activation' && to.path !== '/loading') {
      console.log('🚫 应用未激活，重定向到activation页面')
      next('/activation')
      return
    }
    // 如果状态明确要求激活，且当前在loading页面，允许跳转到activation
    if (currentState === 'activation_required' && to.path === '/activation') {
      console.log('✅ 需要激活，允许跳转到activation页面')
      next()
      return
    }
  }

  // 如果应用还在初始化状态，只允许访问loading页面
  // 除非是未激活状态要跳转到activation页面
  if (currentState === 'loading' || currentState === 'initializing') {
    if (to.path !== '/loading' && !(to.path === '/activation' && !isActivated)) {
      console.log('🚫 应用初始化中，重定向到loading页面')
      next('/loading')
      return
    }
  }

  // 如果应用已激活，不允许访问activation页面
  if (isActivated && to.path === '/activation') {
    console.log('🚫 应用已激活，重定向到主页')
    next('/')
    return
  }

  // 检查路由的状态要求
  if (to.meta.allowedStates && !to.meta.allowedStates.includes(currentState)) {
    console.log('🚫 当前状态不允许访问此路由:', {
      currentState,
      allowedStates: to.meta.allowedStates
    })

    // 根据当前状态重定向到合适的页面
    if (currentState === 'loading' || currentState === 'initializing') {
      next('/loading')
    } else if (!isActivated) {
      next('/activation')
    } else {
      next('/')
    }
    return
  }

  // 检查是否需要激活
  if (to.meta.requiresAuth && !isActivated) {
    console.log('🚫 需要激活才能访问此页面')
    next('/activation')
    return
  }

  console.log('✅ 路由守卫通过')
  next()
})

export default router

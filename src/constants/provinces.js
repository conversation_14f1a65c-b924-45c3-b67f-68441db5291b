// 中国省份数据
export const PROVINCES_DATA = [
  {
    "hasChild": false,
    "id": -1,
    "na": "全国",
    "pinyinAbb": ""
  },
  {
    "hasChild": true,
    "id": 110000,
    "level": "prov",
    "na": "北京"
  },
  {
    "hasChild": true,
    "id": 120000,
    "level": "prov",
    "na": "天津"
  },
  {
    "hasChild": true,
    "id": 130000,
    "level": "prov",
    "na": "河北"
  },
  {
    "hasChild": true,
    "id": 140000,
    "level": "prov",
    "na": "山西"
  },
  {
    "hasChild": true,
    "id": 150000,
    "level": "prov",
    "na": "内蒙古"
  },
  {
    "hasChild": true,
    "id": 210000,
    "level": "prov",
    "na": "辽宁"
  },
  {
    "hasChild": true,
    "id": 220000,
    "level": "prov",
    "na": "吉林"
  },
  {
    "hasChild": true,
    "id": 230000,
    "level": "prov",
    "na": "黑龙江"
  },
  {
    "hasChild": true,
    "id": 310000,
    "level": "prov",
    "na": "上海"
  },
  {
    "hasChild": true,
    "id": 320000,
    "level": "prov",
    "na": "江苏"
  },
  {
    "hasChild": true,
    "id": 330000,
    "level": "prov",
    "na": "浙江"
  },
  {
    "hasChild": true,
    "id": 340000,
    "level": "prov",
    "na": "安徽"
  },
  {
    "hasChild": true,
    "id": 350000,
    "level": "prov",
    "na": "福建"
  },
  {
    "hasChild": true,
    "id": 360000,
    "level": "prov",
    "na": "江西"
  },
  {
    "hasChild": true,
    "id": 370000,
    "level": "prov",
    "na": "山东"
  },
  {
    "hasChild": true,
    "id": 410000,
    "level": "prov",
    "na": "河南"
  },
  {
    "hasChild": true,
    "id": 420000,
    "level": "prov",
    "na": "湖北"
  },
  {
    "hasChild": true,
    "id": 430000,
    "level": "prov",
    "na": "湖南"
  },
  {
    "hasChild": true,
    "id": 440000,
    "level": "prov",
    "na": "广东"
  },
  {
    "hasChild": true,
    "id": 450000,
    "level": "prov",
    "na": "广西"
  },
  {
    "hasChild": true,
    "id": 460000,
    "level": "prov",
    "na": "海南"
  },
  {
    "hasChild": true,
    "id": 500000,
    "level": "prov",
    "na": "重庆"
  },
  {
    "hasChild": true,
    "id": 510000,
    "level": "prov",
    "na": "四川"
  },
  {
    "hasChild": true,
    "id": 520000,
    "level": "prov",
    "na": "贵州"
  },
  {
    "hasChild": true,
    "id": 530000,
    "level": "prov",
    "na": "云南"
  },
  {
    "hasChild": true,
    "id": 540000,
    "level": "prov",
    "na": "西藏"
  },
  {
    "hasChild": true,
    "id": 610000,
    "level": "prov",
    "na": "陕西"
  },
  {
    "hasChild": true,
    "id": 620000,
    "level": "prov",
    "na": "甘肃"
  },
  {
    "hasChild": true,
    "id": 630000,
    "level": "prov",
    "na": "青海"
  },
  {
    "hasChild": true,
    "id": 640000,
    "level": "prov",
    "na": "宁夏"
  },
  {
    "hasChild": true,
    "id": 650000,
    "level": "prov",
    "na": "新疆"
  },
  {
    "hasChild": true,
    "id": 710000,
    "level": "prov",
    "na": "台湾"
  },
  {
    "hasChild": true,
    "id": 810000,
    "level": "prov",
    "na": "香港"
  },
  {
    "hasChild": true,
    "id": 820000,
    "level": "prov",
    "na": "澳门"
  },
  {
    "hasChild": true,
    "id": 990000,
    "level": "prov",
    "na": "海外"
  }
];

// 将省份数据转换为 Naive UI Select 组件需要的格式
export const PROVINCE_OPTIONS = PROVINCES_DATA.map(province => ({
  label: province.na,
  value: province.id,
  province: province
}));

// 根据省份ID获取省份信息
export function getProvinceById(id) {
  return PROVINCES_DATA.find(province => province.id === id);
}

// 根据省份名称获取省份信息
export function getProvinceByName(name) {
  return PROVINCES_DATA.find(province => province.na === name);
}

import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 主题存储键名
  const THEME_STORAGE_KEY = 'goldfish-theme-preference'
  
  // 主题状态
  const isDark = ref(getStoredTheme())
  
  // 从本地存储获取主题设置
  function getStoredTheme() {
    try {
      const stored = localStorage.getItem(THEME_STORAGE_KEY)
      return stored ? JSON.parse(stored) : true // 默认夜间模式
    } catch (error) {
      console.warn('读取主题设置失败，使用默认夜间模式:', error)
      return true
    }
  }
  
  // 保存主题设置到本地存储
  function saveThemeToStorage(isDarkMode) {
    try {
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(isDarkMode))
      console.log('💾 主题设置已保存:', isDarkMode ? '夜间模式' : '日间模式')
    } catch (error) {
      console.error('保存主题设置失败:', error)
    }
  }
  
  // 切换主题
  function toggleTheme() {
    console.log('🎨 主题切换前:', isDark.value)
    isDark.value = !isDark.value
    console.log('🎨 主题切换后:', isDark.value)
    
    // 保存到本地存储
    saveThemeToStorage(isDark.value)
    
    // 更新 HTML 根元素的 class
    updateDOMTheme()
  }
  
  // 设置主题
  function setTheme(darkMode) {
    isDark.value = darkMode
    saveThemeToStorage(darkMode)
    updateDOMTheme()
  }
  
  // 更新 DOM 主题类
  function updateDOMTheme() {
    if (isDark.value) {
      document.documentElement.classList.add('dark')
      console.log('🌙 添加 dark 类')
    } else {
      document.documentElement.classList.remove('dark')
      console.log('☀️ 移除 dark 类')
    }
  }
  
  // 初始化主题
  function initializeTheme() {
    console.log('🎨 初始化主题状态:', isDark.value ? '夜间模式' : '日间模式')
    updateDOMTheme()
  }
  
  return {
    // 状态
    isDark,
    
    // 方法
    toggleTheme,
    setTheme,
    initializeTheme
  }
})

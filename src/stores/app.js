import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { isTauriEnv, safeListen, safeInvoke, isDev } from '../utils/tauri'

export const useAppStore = defineStore('app', () => {
  // 状态
  const appState = ref('loading') // loading, initializing, not_activated, activation_required, activated, running
  const isActivated = ref(false)
  const loadingMessage = ref('正在启动应用...')
  const isInitialized = ref(false)

  // 计算属性
  const canAccessHome = computed(() => isActivated.value && appState.value === 'activated')
  const canAccessActivation = computed(() => !isActivated.value && appState.value !== 'loading')
  const shouldShowLoading = computed(() => appState.value === 'loading' || appState.value === 'initializing')

  // 动作
  const setAppState = (newState) => {
    console.log('🔄 应用状态变更:', appState.value, '->', newState)
    appState.value = newState
  }

  const setActivated = (activated) => {
    console.log('🔐 激活状态变更:', isActivated.value, '->', activated)
    isActivated.value = activated
    if (activated) {
      setAppState('activated')
    }
  }

  const setLoadingMessage = (message) => {
    loadingMessage.value = message
  }

  // 初始化事件监听
  const initializeEventListeners = async () => {
    if (isInitialized.value) return

    console.log('🎧 开始初始化应用事件监听器')
    const isWindows = typeof navigator !== 'undefined' &&
      (navigator.userAgent.includes('Windows') || navigator.platform.includes('Win'));

    console.log('🔍 环境检测:', {
      isDev: isDev(),
      isTauriEnv: isTauriEnv(),
      isWindows: isWindows
    })

    // 开发环境下模拟激活状态
    if (isDev() && !isTauriEnv()) {
      console.log('🔧 开发环境：模拟激活状态')
      setActivated(true)
      setAppState('activated')
      isInitialized.value = true
      return
    }

    console.log('🎧 Tauri环境：初始化真实事件监听器')

    try {
      // 监听加载状态更新
      await safeListen('loading_status', (event) => {
        console.log('📡 收到加载状态:', event.payload)
        setLoadingMessage(event.payload)
      })

      // 监听激活成功事件
      await safeListen('app_activation_success', () => {
        console.log('🎉 收到激活成功事件')
        setActivated(true)
        setAppState('activated')
      })

      // 监听需要激活事件
      await safeListen('app_activation_required', () => {
        console.log('🔐 收到需要激活事件')
        setActivated(false)
        setAppState('activation_required')
      })

      // 监听激活完成事件
      await safeListen('app_activation_completed', () => {
        console.log('✅ 收到激活完成事件')
        setActivated(true)
        setAppState('running')
      })

      // 监听应用初始化成功
      await safeListen('app_init_success', () => {
        console.log('✅ 应用初始化成功')
        // 不直接改变状态，等待激活检查结果
      })

      // 监听应用初始化错误
      await safeListen('app_init_error', (event) => {
        console.error('❌ 应用初始化失败:', event.payload)
        setLoadingMessage(`初始化失败: ${event.payload}`)
        setAppState('error')
      })

      isInitialized.value = true
      console.log('✅ 事件监听器初始化完成')

      // Windows特定：防止时序问题导致错过后端事件
      if (isWindows) {
        setTimeout(async () => {
          if (appState.value === 'loading') {
            try {
              const activationStatus = await safeInvoke('auth_check_activation_status')
              if (activationStatus) {
                setActivated(true)
                setAppState('activated')
              } else {
                setActivated(false)
                setAppState('activation_required')
              }
            } catch (error) {
              console.error('Windows激活状态检查失败:', error)
              setActivated(false)
              setAppState('activation_required')
            }
          }
        }, 500)
      }

    } catch (error) {
      console.error('❌ 初始化事件监听器失败:', error)

      // Windows特定：如果事件监听器初始化失败，使用备用方案
      if (isWindows) {
        console.log('🪟 Windows环境：事件监听器失败，使用备用激活检查')
        setTimeout(async () => {
          try {
            const activationStatus = await safeInvoke('auth_check_activation_status')
            if (activationStatus) {
              setActivated(true)
              setAppState('activated')
            } else {
              setActivated(false)
              setAppState('activation_required')
            }
          } catch (backupError) {
            console.error('🪟 Windows备用激活检查也失败:', backupError)
            // 最后的备用方案：假设未激活
            setActivated(false)
            setAppState('activation_required')
          }
        }, 3000)
      }
    }
  }

  // 检查激活状态
  const checkActivationStatus = async () => {
    try {
      console.log('🔍 检查激活状态')
      const result = await safeInvoke('auth_check_activation_status')
      setActivated(result)

      if (result) {
        setAppState('activated')
      } else {
        setAppState('not_activated')
      }

      return result
    } catch (error) {
      console.error('❌ 检查激活状态失败:', error)
      setActivated(false)
      setAppState('not_activated')
      return false
    }
  }

  // 重新检查激活状态
  const recheckActivation = async () => {
    try {
      console.log('🔄 重新检查激活状态')
      const result = await safeInvoke('app_recheck_activation')
      return result
    } catch (error) {
      console.error('❌ 重新检查激活状态失败:', error)
      return false
    }
  }

  // 激活应用
  const activateApp = async (activationCode) => {
    try {
      console.log('🔐 尝试激活应用')
      const result = await safeInvoke('auth_validate_activation_code', {
        activationCode: activationCode.trim()
      })

      if (result.success) {
        // 调用激活成功处理
        await safeInvoke('app_activation_success')
        return { success: true }
      } else {
        return { success: false, error: result.error || '激活码无效' }
      }
    } catch (error) {
      console.error('❌ 激活失败:', error)
      return { success: false, error: error.toString() }
    }
  }

  // 导航到指定页面（带状态检查）
  const navigateTo = (router, path) => {
    const currentState = appState.value
    const activated = isActivated.value

    console.log('🧭 尝试导航到:', path, { currentState, activated })

    // 根据当前状态和激活状态决定是否允许导航
    if (path === '/' && !activated) {
      console.log('🚫 未激活，无法访问主页')
      router.push('/activation')
      return false
    }

    if (path === '/activation' && activated) {
      console.log('🚫 已激活，无法访问激活页面')
      router.push('/')
      return false
    }

    if (currentState === 'loading' && path !== '/loading') {
      console.log('🚫 应用加载中，无法访问其他页面')
      return false
    }

    router.push(path)
    return true
  }

  return {
    // 状态
    appState,
    isActivated,
    loadingMessage,
    isInitialized,

    // 计算属性
    canAccessHome,
    canAccessActivation,
    shouldShowLoading,

    // 动作
    setAppState,
    setActivated,
    setLoadingMessage,
    initializeEventListeners,
    checkActivationStatus,
    recheckActivation,
    activateApp,
    navigateTo
  }
})

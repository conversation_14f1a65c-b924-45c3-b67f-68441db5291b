<template>
  <div class="account-manage-page">
    <!-- 统计信息卡片 -->
    <div class="stats-cards">
      <n-card class="stat-card" size="small">
        <n-statistic label="总账号数" :value="stats.total_accounts || 0" />
      </n-card>
      <n-card class="stat-card" size="small">
        <n-statistic label="可用账号" :value="stats.available_accounts || 0" />
      </n-card>
      <n-card class="stat-card" size="small">
        <n-statistic label="活跃账号" :value="stats.active_accounts || 0" />
      </n-card>
      <n-card class="stat-card" size="small">
        <n-statistic label="错误账号" :value="stats.error_accounts || 0" />
      </n-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <n-space>
        <n-button type="primary" @click="showAddModal = true">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          添加账号
        </n-button>
        <n-button @click="validateAllCookies" :loading="validatingAll">
          <template #icon>
            <n-icon><CheckmarkCircleOutline /></n-icon>
          </template>
          验证所有Cookie
        </n-button>
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
      </n-space>
    </div>

    <!-- 账号列表 -->
    <n-card class="account-list-card">
      <n-data-table
        :columns="columns"
        :data="accounts"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
      />
    </n-card>

    <!-- 添加账号模态框 -->
    <n-modal v-model:show="showAddModal" preset="dialog" title="添加账号">
      <template #default>
        <n-tabs v-model:value="addAccountTab" type="line">
          <!-- 手动Cookie添加 -->
          <n-tab-pane name="manual" tab="手动Cookie">
            <n-form
              ref="manualFormRef"
              :model="manualForm"
              :rules="manualFormRules"
            >
              <n-form-item label="Cookie值" path="cookie">
                <n-input
                  v-model:value="manualForm.cookie"
                  type="textarea"
                  placeholder="请粘贴完整的Cookie字符串"
                  :rows="6"
                />
              </n-form-item>
              <n-form-item label="描述" path="description">
                <n-input
                  v-model:value="manualForm.description"
                  placeholder="账号描述（可选）"
                />
              </n-form-item>
              <n-alert type="info" style="margin-top: 12px">
                系统将自动验证Cookie并获取用户信息，账号名将使用用户昵称。
              </n-alert>
            </n-form>
          </n-tab-pane>

          <!-- 自动登录获取 -->
          <n-tab-pane name="auto" tab="自动登录">
            <div style="text-align: center; padding: 20px">
              <n-icon size="48" color="#18a058">
                <LogInOutline />
              </n-icon>
              <h3>自动登录获取Cookie</h3>
              <p>
                点击下方按钮将打开登录窗口，完成登录后关闭窗口即可自动添加账号。
              </p>
              <n-form ref="autoFormRef" :model="autoForm">
                <n-form-item label="描述">
                  <n-input
                    v-model:value="autoForm.description"
                    placeholder="账号描述（可选）"
                  />
                </n-form-item>
              </n-form>
            </div>
          </n-tab-pane>
        </n-tabs>
      </template>
      <template #action>
        <n-space>
          <n-button @click="showAddModal = false">取消</n-button>
          <n-button
            v-if="addAccountTab === 'manual'"
            type="primary"
            @click="handleAddAccountManual"
            :loading="addingManual"
          >
            验证并添加
          </n-button>
          <n-button
            v-if="addAccountTab === 'auto'"
            type="primary"
            @click="handleAddAccountAuto"
            :loading="addingAuto"
          >
            打开登录窗口
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 登录添加账号模态框 -->
    <n-modal
      v-model:show="showLoginModal"
      preset="dialog"
      title="通过登录添加账号"
    >
      <template #default>
        <n-form ref="loginFormRef" :model="loginForm" :rules="loginFormRules">
          <n-form-item label="账号名称" path="name">
            <n-input
              v-model:value="loginForm.name"
              placeholder="请输入账号名称"
            />
          </n-form-item>
          <n-form-item label="描述" path="description">
            <n-input
              v-model:value="loginForm.description"
              type="textarea"
              placeholder="请输入账号描述（可选）"
              :rows="3"
            />
          </n-form-item>
          <n-alert type="info" style="margin-top: 16px">
            点击确定后将打开登录窗口，请在登录窗口中完成闲鱼登录，登录成功后关闭窗口即可自动获取Cookie。
          </n-alert>
        </n-form>
      </template>
      <template #action>
        <n-space>
          <n-button @click="showLoginModal = false">取消</n-button>
          <n-button
            type="primary"
            @click="handleAddAccountViaLogin"
            :loading="addingViaLogin"
          >
            开始登录
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 编辑Cookie模态框 -->
    <n-modal
      v-model:show="showCookieModal"
      preset="dialog"
      :title="`更新Cookie - ${currentAccountName}`"
      style="width: 600px"
    >
      <template #default>
        <n-tabs v-model:value="updateCookieTab" type="line">
          <!-- 手动更新Cookie -->
          <n-tab-pane name="manual" tab="手动更新">
            <n-form
              ref="cookieFormRef"
              :model="cookieForm"
              :rules="cookieFormRules"
            >
              <n-form-item label="Cookie值" path="cookie_value">
                <n-input
                  v-model:value="cookieForm.cookie_value"
                  type="textarea"
                  placeholder="请输入Cookie值"
                  :rows="8"
                />
              </n-form-item>
              <n-alert type="info" style="margin-top: 12px">
                请粘贴完整的Cookie字符串，通常包含多个键值对，用分号分隔。
                系统将自动验证Cookie并更新账号信息。
              </n-alert>
            </n-form>
          </n-tab-pane>

          <!-- 自动登录更新 -->
          <n-tab-pane name="auto" tab="自动登录">
            <div style="text-align: center; padding: 20px">
              <n-icon size="48" color="#18a058">
                <LogInOutline />
              </n-icon>
              <h3>自动登录更新Cookie</h3>
              <p>
                点击下方按钮将为账号
                <strong>{{ currentAccountName }}</strong> 打开专属登录窗口，
                完成登录后关闭窗口即可自动更新Cookie。
              </p>
              <n-alert type="info" style="margin-top: 16px">
                此登录窗口使用账号专属的数据隔离环境，确保登录状态不会相互影响。
              </n-alert>
            </div>
          </n-tab-pane>
        </n-tabs>
      </template>
      <template #action>
        <n-space>
          <n-button @click="showCookieModal = false">取消</n-button>
          <n-button
            v-if="updateCookieTab === 'manual'"
            type="primary"
            @click="handleUpdateCookie"
            :loading="updatingCookie"
          >
            验证并更新
          </n-button>
          <n-button
            v-if="updateCookieTab === 'auto'"
            type="primary"
            @click="handleOpenLoginWindow"
            :loading="openingLoginWindow"
          >
            打开登录窗口
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, h } from "vue";
import {
  NCard,
  NStatistic,
  NSpace,
  NButton,
  NIcon,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NInput,
  NSwitch,
  NTag,
  NPopconfirm,
  NAlert,
  NTabs,
  NTabPane,
  useMessage,
} from "naive-ui";
import {
  AddOutline,
  CheckmarkCircleOutline,
  RefreshOutline,
  CreateOutline,
  TrashOutline,
  CheckmarkOutline,
  CloseOutline,
  LogInOutline,
} from "@vicons/ionicons5";
import { safeInvoke } from "../utils/tauri";
import { listen } from "@tauri-apps/api/event";

const message = useMessage();

// 响应式数据
const loading = ref(false);
const validatingAll = ref(false);
const adding = ref(false);
const addingViaLogin = ref(false);
const addingManual = ref(false);
const addingAuto = ref(false);
const updatingCookie = ref(false);
const openingLoginWindow = ref(false);

const accounts = ref([]);
const stats = ref({});

// 模态框状态
const showAddModal = ref(false);
const showLoginModal = ref(false);
const showCookieModal = ref(false);
const addAccountTab = ref("auto");
const updateCookieTab = ref("auto");
const currentAccountId = ref("");
const currentAccountName = ref("");

// 表单数据
const addForm = reactive({
  name: "",
  description: "",
  cookie: "",
});

const manualForm = reactive({
  cookie: "",
  description: "",
});

const autoForm = reactive({
  description: "",
});

const loginForm = reactive({
  name: "",
  description: "",
});

const cookieForm = reactive({
  cookie_value: "",
});

// 表单验证规则
const addFormRules = {
  name: [{ required: true, message: "请输入账号名称", trigger: "blur" }],
};

const manualFormRules = {
  cookie: [{ required: true, message: "请输入Cookie值", trigger: "blur" }],
};

const loginFormRules = {
  name: [{ required: true, message: "请输入账号名称", trigger: "blur" }],
};

const cookieFormRules = {
  cookie_value: [
    { required: true, message: "请输入Cookie值", trigger: "blur" },
  ],
};

// 表单引用
const manualFormRef = ref(null);
const autoFormRef = ref(null);
const loginFormRef = ref(null);
const cookieFormRef = ref(null);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
});

// 表格列配置
const columns = [
  {
    title: "用户信息",
    key: "userInfo",
    width: 200,
    render(row) {
      const avatar = row.metadata?.avatar;
      const nickname = row.metadata?.nickname || row.name;
      const userId = row.metadata?.user_id;

      return h(
        "div",
        { style: "display: flex; align-items: center; gap: 12px;" },
        [
          // 头像
          avatar
            ? h("img", {
                src: avatar,
                style:
                  "width: 40px; height: 40px; border-radius: 50%; object-fit: cover; border: 2px solid #f0f0f0;",
                onError: (e) => {
                  // 头像加载失败时显示默认头像
                  e.target.src =
                    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNmNWY1ZjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iI2Q5ZDlkOSIvPgo8cGF0aCBkPSJNMTAgMzJjMC02IDQtMTAgMTAtMTBzMTAgNCAxMCAxMCIgZmlsbD0iI2Q5ZDlkOSIvPgo8L3N2Zz4K";
                },
              })
            : h(
                "div",
                {
                  style:
                    "width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white !important; font-weight: bold; font-size: 16px;",
                },
                nickname ? nickname.charAt(0).toUpperCase() : "?"
              ),

          // 用户信息
          h("div", { style: "flex: 1; min-width: 0;" }, [
            h(
              "div",
              {
                style:
                  "font-weight: 500; color: white; font-size: 14px; margin-bottom: 2px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",
              },
              nickname
            ),
            userId
              ? h(
                  "div",
                  {
                    style:
                      "font-size: 12px; color: #999; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",
                  },
                  `ID: ${userId}`
                )
              : null,
          ]),
        ]
      );
    },
  },
  {
    title: "状态",
    key: "status",
    width: 100,
    render(row) {
      const statusMap = {
        Active: { type: "success", text: "活跃" },
        Inactive: { type: "default", text: "失效" },
        InUse: { type: "info", text: "使用中" },
        Error: { type: "error", text: "错误" },
      };
      const status = statusMap[row.status] || { type: "default", text: "未知" };
      return h(NTag, { type: status.type }, { default: () => status.text });
    },
  },
  {
    title: "启用状态",
    key: "enabled",
    width: 100,
    render(row) {
      return h(NSwitch, {
        value: row.enabled,
        onUpdateValue: (value) => handleToggleEnabled(row.id, value),
      });
    },
  },
  {
    title: "优先级",
    key: "priority",
    width: 80,
  },
  {
    title: "总调用",
    key: "stats.total_calls",
    width: 80,
    render(row) {
      return row.stats?.total_calls || 0;
    },
  },
  {
    title: "成功率",
    key: "success_rate",
    width: 80,
    render(row) {
      const total = row.stats?.total_calls || 0;
      const success = row.stats?.success_calls || 0;
      if (total === 0) return "0%";
      return `${Math.round((success / total) * 100)}%`;
    },
  },
  {
    title: "最后使用",
    key: "stats.last_used_at",
    width: 150,
    render(row) {
      if (!row.stats?.last_used_at) return "从未使用";
      return new Date(row.stats.last_used_at).toLocaleString();
    },
  },
  {
    title: "操作",
    key: "actions",
    width: 200,
    render(row) {
      return h(
        NSpace,
        { size: "small" },
        {
          default: () => [
            h(
              NButton,
              {
                size: "small",
                type: "primary",
                onClick: () => handleEditCookie(row),
              },
              { default: () => "Cookie" }
            ),
            h(
              NButton,
              {
                size: "small",
                onClick: () => handleValidateCookie(row.id),
              },
              { default: () => "验证" }
            ),
            h(
              NPopconfirm,
              {
                onPositiveClick: () => handleDeleteAccount(row.id),
              },
              {
                trigger: () =>
                  h(
                    NButton,
                    {
                      size: "small",
                      type: "error",
                    },
                    { default: () => "删除" }
                  ),
                default: () => "确定删除此账号吗？",
              }
            ),
          ],
        }
      );
    },
  },
];

// 方法定义
const refreshData = async () => {
  loading.value = true;
  console.log("🔄 开始刷新账号数据");

  try {
    const [accountsData, statsData] = await Promise.all([
      safeInvoke("account_get_all"),
      safeInvoke("account_get_stats"),
    ]);

    console.log("📊 获取到账号数据:", accountsData);
    console.log("📈 获取到统计数据:", statsData);

    accounts.value = accountsData || [];
    stats.value = statsData || {};

    console.log("✅ 数据刷新完成");
  } catch (error) {
    console.error("❌ 刷新数据失败:", error);
    message.error(`刷新数据失败: ${error}`);
  } finally {
    loading.value = false;
  }
};

// 手动Cookie添加账号
const handleAddAccountManual = async () => {
  addingManual.value = true;
  console.log("🚀 开始手动Cookie添加账号");

  try {
    // 验证表单
    await manualFormRef.value?.validate();

    // 先验证Cookie并获取用户信息
    console.log("🔍 验证Cookie并获取用户信息");
    const userInfo = await safeInvoke("account_get_user_info", {
      cookie: manualForm.cookie,
    });

    if (!userInfo.is_valid) {
      message.error("Cookie验证失败，请检查Cookie是否正确");
      return;
    }

    if (!userInfo.nickname) {
      message.error("无法获取用户昵称，请检查Cookie是否完整");
      return;
    }

    console.log("✅ 用户信息获取成功:", userInfo);

    // 使用昵称作为账号名创建账号
    const response = await safeInvoke("account_add_with_user_info", {
      nickname: userInfo.nickname,
      userId: userInfo.user_id,
      cookie: manualForm.cookie,
      description: manualForm.description || null,
    });

    console.log("✅ 账号添加响应:", response);

    // 根据后端返回的is_update标志显示相应消息
    if (response.is_update) {
      message.success(`账号 "${userInfo.nickname}" 已存在，Cookie和信息已更新`);
    } else {
      message.success(`账号 "${userInfo.nickname}" 添加成功`);
    }

    // 清空表单并关闭模态框
    showAddModal.value = false;
    manualForm.cookie = "";
    manualForm.description = "";
    await refreshData();
    console.log("✅ 手动Cookie添加流程完成");
  } catch (error) {
    console.error("❌ 手动Cookie添加失败:", error);
    message.error(`添加账号失败: ${error}`);
  } finally {
    addingManual.value = false;
  }
};

// 自动登录添加账号
const handleAddAccountAuto = async () => {
  addingAuto.value = true;
  console.log("🚀 开始自动登录添加账号");

  try {
    // 打开带ID的登录窗口（使用会话管理）
    await safeInvoke("app_handle_login", {
      description: autoForm.description || null,
    });

    message.info("登录窗口已打开，请完成登录后关闭窗口");

    // 关闭添加账号模态框
    showAddModal.value = false;
    autoForm.description = "";

    console.log("✅ 自动登录流程启动完成");
  } catch (error) {
    console.error("❌ 自动登录添加失败:", error);
    message.error(`打开登录窗口失败: ${error}`);
  } finally {
    addingAuto.value = false;
  }
};

// 保留原有的添加账号方法（用于兼容）
const handleAddAccount = async () => {
  adding.value = true;
  console.log("🚀 开始添加账号:", {
    name: addForm.name,
    description: addForm.description,
    hasCookie: !!addForm.cookie,
  });

  try {
    const response = await safeInvoke("account_add", {
      request: {
        name: addForm.name,
        description: addForm.description || null,
        cookie: addForm.cookie || null,
      },
    });

    console.log("✅ 账号添加响应:", response);

    // 处理返回的AccountResponse结构
    if (response && response.message) {
      message.success(response.message);
    } else {
      message.success("账号添加成功");
    }

    showAddModal.value = false;
    addForm.name = "";
    addForm.description = "";
    addForm.cookie = "";
    await refreshData();
    console.log("✅ 账号添加流程完成");
  } catch (error) {
    console.error("❌ 账号添加失败:", error);
    message.error(`添加账号失败: ${error}`);
  } finally {
    adding.value = false;
    console.log("🏁 账号添加流程结束");
  }
};

const handleAddAccountViaLogin = async () => {
  addingViaLogin.value = true;
  console.log("🔑 开始通过登录添加账号:", {
    name: loginForm.name,
    description: loginForm.description,
  });

  try {
    const response = await safeInvoke("account_add_via_login", {
      request: {
        name: loginForm.name,
        description: loginForm.description || null,
      },
    });

    console.log("✅ 登录添加账号响应:", response);

    // 处理返回的AccountResponse结构
    if (response && response.message) {
      message.success(response.message);
    } else {
      message.success("账号通过登录添加成功");
    }

    showLoginModal.value = false;
    loginForm.name = "";
    loginForm.description = "";
    await refreshData();
    console.log("✅ 登录添加账号流程完成");
  } catch (error) {
    console.error("❌ 登录添加账号失败:", error);
    message.error(`登录添加账号失败: ${error}`);
  } finally {
    addingViaLogin.value = false;
    console.log("🏁 登录添加账号流程结束");
  }
};

const handleEditCookie = (account) => {
  console.log("🍪 编辑Cookie - 账号数据:", account);
  console.log("🍪 Cookie对象:", account.cookie);
  console.log("🍪 Cookie值:", account.cookie?.value);

  currentAccountId.value = account.id;
  currentAccountName.value = account.name;
  // 回显当前Cookie值
  const cookieValue = account.cookie?.value || "";
  console.log("🍪 设置Cookie值:", cookieValue);
  cookieForm.cookie_value = cookieValue;
  // 重置Tab到手动更新
  updateCookieTab.value = "auto";
  showCookieModal.value = true;
};

const handleUpdateCookie = async () => {
  updatingCookie.value = true;
  try {
    await safeInvoke("account_update_cookie", {
      accountId: currentAccountId.value,
      cookieValue: cookieForm.cookie_value,
    });
    message.success("Cookie更新成功");
    showCookieModal.value = false;
    cookieForm.cookie_value = ""; // 清空表单
    await refreshData();
  } catch (error) {
    message.error(`更新Cookie失败: ${error}`);
  } finally {
    updatingCookie.value = false;
  }
};

const handleOpenLoginWindow = async () => {
  openingLoginWindow.value = true;
  try {
    console.log(
      `🔑 为账号 ${currentAccountName.value} (ID: ${currentAccountId.value}) 打开登录窗口`
    );

    // 调用带账号ID的自动更新Cookie方法
    await safeInvoke("account_auto_update_cookie", {
      accountId: currentAccountId.value,
    });

    message.info(
      `已为账号 ${currentAccountName.value} 打开专属登录窗口，请完成登录后关闭窗口`
    );

    // 关闭Cookie编辑模态框，因为登录完成后会自动更新
    showCookieModal.value = false;
    cookieForm.cookie_value = ""; // 清空表单
  } catch (error) {
    console.error("打开登录窗口失败:", error);
    message.error(`打开登录窗口失败: ${error}`);
  } finally {
    openingLoginWindow.value = false;
  }
};

const handleValidateCookie = async (accountId) => {
  try {
    const isValid = await safeInvoke("account_validate_cookie", {
      accountId: accountId,
    });
    if (isValid) {
      message.success("Cookie验证成功");
    } else {
      message.warning("Cookie已失效");
    }
    await refreshData();
  } catch (error) {
    message.error(`验证Cookie失败: ${error}`);
  }
};

const validateAllCookies = async () => {
  validatingAll.value = true;
  try {
    const results = await safeInvoke("account_validate_all_cookies");
    const validCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    message.info(`验证完成: ${validCount}/${totalCount} 个账号Cookie有效`);
    await refreshData();
  } catch (error) {
    message.error(`批量验证失败: ${error}`);
  } finally {
    validatingAll.value = false;
  }
};

const handleToggleEnabled = async (accountId, enabled) => {
  try {
    await safeInvoke("account_set_enabled", { accountId: accountId, enabled });
    message.success(`账号已${enabled ? "启用" : "禁用"}`);
    await refreshData();
  } catch (error) {
    message.error(`操作失败: ${error}`);
  }
};

const handleDeleteAccount = async (accountId) => {
  try {
    await safeInvoke("account_remove", { accountId: accountId });
    message.success("账号删除成功");
    await refreshData();
  } catch (error) {
    message.error(`删除账号失败: ${error}`);
  }
};

// 事件监听器引用
let accountEventUnlisten = null;
let configEventUnlisten = null;

// 生命周期
onMounted(async () => {
  // 初始加载数据
  refreshData();

  // 监听账号事件
  accountEventUnlisten = await listen("account_event", (event) => {
    console.log("📡 收到账号事件:", event.payload);
    const { event_type, message } = event.payload;

    // 根据事件类型显示消息并刷新数据
    switch (event_type) {
      case "account_added":
      case "login_session_success":
        message.success(message || "账号添加成功");
        refreshData();
        break;
      case "account_removed":
        message.info(message || "账号已删除");
        refreshData();
        break;
      case "cookie_updated":
      case "account_cookie_updated":
        message.success(message || "Cookie更新成功");
        refreshData();
        break;
      case "account_updated":
        message.success(message || "账号信息已更新");
        refreshData();
        break;
      case "account_error":
        message.error(message || "账号操作失败");
        break;
      default:
        console.log("未处理的账号事件:", event_type);
    }
  });

  // 监听配置更新事件
  configEventUnlisten = await listen("account_config_updated", () => {
    console.log("📡 收到配置更新事件，刷新数据");
    refreshData();
  });
});

onUnmounted(() => {
  // 清理事件监听器
  if (accountEventUnlisten) {
    accountEventUnlisten();
  }
  if (configEventUnlisten) {
    configEventUnlisten();
  }
});
</script>

<style scoped>
.account-manage-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: var(--text-color-2);
  font-size: 14px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.toolbar {
  margin-bottom: 16px;
}

.account-list-card {
  margin-bottom: 24px;
}
</style>

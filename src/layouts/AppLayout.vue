<template>
  <n-config-provider :theme="theme">
    <n-global-style />
    <n-message-provider>
      <div class="app-layout h-screen flex flex-col" :class="{ dark: isDark }">
        <!-- 顶部导航栏 -->
        <AppHeader class="flex-shrink-0" />

        <!-- 主体内容区 -->
        <div class="flex flex-1 overflow-hidden">
          <!-- 左侧菜单栏 -->
          <AppSidebar class="flex-shrink-0" />

          <!-- 右侧内容区 -->
          <div class="flex-1 flex flex-col overflow-hidden">
            <!-- 内容区域 -->
            <main class="flex-1 overflow-auto app-main box-border pr-6 pb-6">
              <router-view />
            </main>
          </div>
        </div>
      </div>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup>
import { computed } from "vue";
import {
  darkTheme,
  lightTheme,
  NConfigProvider,
  NGlobalStyle,
  NMessageProvider,
} from "naive-ui";
import AppHeader from "./AppHeader.vue";
import AppSidebar from "./AppSidebar.vue";
import { useThemeStore } from "../stores/theme";

// 使用主题 store
const themeStore = useThemeStore();
const theme = computed(() => (themeStore.isDark ? darkTheme : lightTheme));
const isDark = computed(() => themeStore.isDark);

// 主题已在 Root.vue 中初始化，这里不需要重复初始化

// 调试信息
console.log("🔍 AppLayout 主题状态:", {
  isDark: themeStore.isDark,
  toggleTheme: typeof themeStore.toggleTheme,
});
</script>

<style scoped>
.app-layout {
  background-color: var(--n-base-color);
}

.app-main {
  background-color: var(--n-base-color);
  transition: all 0.3s ease;
}
</style>



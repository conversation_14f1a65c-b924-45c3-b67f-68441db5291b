<template>
  <aside class="app-sidebar">
    <div class="p-4 pt-0">
      <!-- 菜单列表 -->
      <n-menu
        :value="activeKey"
        :options="menuOptions"
        :collapsed="collapsed"
        :collapsed-width="64"
        :collapsed-icon-size="20"
        :indent="24"
        :root-indent="12"
        @update:value="handleMenuSelect"
      />
    </div>

    <!-- 底部折叠按钮 -->
    <div class="absolute bottom-4 left-4 right-4">
      <n-button
        quaternary
        block
        @click="toggleCollapsed"
        :title="collapsed ? '展开菜单' : '收起菜单'"
      >
        <template #icon>
          <n-icon :size="16">
            <ArrowForwardOutline v-if="collapsed" />
            <ArrowBackOutline v-else />
          </n-icon>
        </template>
        <span v-if="!collapsed">{{ collapsed ? "展开" : "收起" }}</span>
      </n-button>
    </div>
  </aside>
</template>

<script setup>
import { ref, computed, h } from "vue";
import { useRouter, useRoute } from "vue-router";
import { NMenu, NButton, NIcon } from "naive-ui";
import {
  AnalyticsOutline,
  SettingsOutline,
  DocumentTextOutline,
  DesktopOutline,
  ArrowForwardOutline,
  ArrowBackOutline,
  PeopleOutline,
  BugOutline,
} from "@vicons/ionicons5";

const router = useRouter();
const route = useRoute();

// 菜单折叠状态
const collapsed = ref(false);

// 当前激活的菜单项
const activeKey = computed(() => route.name);

// 菜单选项
const menuOptions = [
  {
    label: "监控面板",
    key: "monitor",
    icon: renderIcon(AnalyticsOutline),
  },
  {
    label: "账号管理",
    key: "accounts",
    icon: renderIcon(PeopleOutline),
  },
  {
    label: "监控设置",
    key: "settings",
    icon: renderIcon(SettingsOutline),
  },
  {
    label: "日志查看",
    key: "logs",
    icon: renderIcon(DocumentTextOutline),
  },
  {
    label: "调试面板",
    key: "debug",
    icon: renderIcon(BugOutline),
  },
];

// 渲染图标
function renderIcon(icon) {
  return () => h(NIcon, { size: 18 }, { default: () => h(icon) });
}

// 菜单选择处理
const handleMenuSelect = (key) => {
  if (key && key !== route.name) {
    router.push({ name: key });
  }
};

// 切换折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
};
</script>

<style scoped>
.app-sidebar {
  /* width: 240px; */
  position: relative;
  /* transition: width 0.3s ease; */
  background-color: var(--n-card-color);
  border-right: 1px solid var(--n-border-color);
}

.app-sidebar :deep(.n-menu--collapsed) {
  width: 64px;
}

/* 自定义菜单样式 */
.app-sidebar :deep(.n-menu-item) {
  margin: 4px 0;
  border-radius: 8px;
}

.app-sidebar :deep(.n-menu-item--selected) {
  background-color: var(--n-primary-color-suppl);
  color: var(--n-primary-color);
  font-weight: 600;
}
.app-sidebar :deep(.n-menu-item:hover) {
  background-color: var(--n-primary-color-hover);
}

.app-sidebar :deep(.n-menu-item-content) {
  padding: 12px 16px;
}

.app-sidebar :deep(.n-menu-item-content-header) {
  font-size: 14px;
}
</style>

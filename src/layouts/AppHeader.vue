<template>
  <header class="app-header px-6 py-3">
    <div class="flex items-center justify-between">
      <!-- 左侧：应用标题和状态 -->
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <div class="app-logo">
            <span class="logo-icon">🐠</span>
          </div>
          <h1 class="app-title">闲鱼监控助手</h1>
        </div>

        <!-- 监控状态指示器 -->
        <div class="flex items-center space-x-2">
          <n-tag
            :type="monitorStore.isRunning ? 'success' : 'default'"
            size="small"
            :bordered="false"
          >
            <template #icon>
              <div
                class="status-dot"
                :class="
                  monitorStore.isRunning ? 'status-running' : 'status-stopped'
                "
              ></div>
            </template>
            {{ monitorStore.isRunning ? "监控中" : "已停止" }}
          </n-tag>
        </div>
      </div>

      <!-- 右侧：时间显示和操作按钮 -->
      <div class="flex items-center space-x-4">
        <!-- 服务器时间显示 -->
        <ServerTimeDisplay />

        <!-- 主题切换 -->
        <n-button
          quaternary
          circle
          @click="themeStore.toggleTheme"
          :title="themeStore.isDark ? '切换到亮色主题' : '切换到暗色主题'"
        >
          <template #icon>
            <n-icon :size="18">
              <MoonOutline v-if="themeStore.isDark" />
              <SunnyOutline v-else />
            </n-icon>
          </template>
        </n-button>
      </div>
    </div>
  </header>
</template>

<script setup>
import { onMounted } from "vue";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
import { isTauriEnv } from "../utils/tauri";
import { NButton, NTag, NIcon } from "naive-ui";
import { MoonOutline, SunnyOutline } from "@vicons/ionicons5";
import { useMonitorStore } from "../stores/monitor";
import { useThemeStore } from "../stores/theme";
import ServerTimeDisplay from "../components/ServerTimeDisplay.vue";

// 使用 store
const monitorStore = useMonitorStore();
const themeStore = useThemeStore();

// 直接使用 store，不要赋值给本地变量
// const monitorStatus = monitorStore.isRunning; // 这样会失去响应式
// const isDark = themeStore.isDark; // 这样会失去响应式

// 组件挂载时初始化
onMounted(() => {
  monitorStore.initialize();
});

// 调试信息
console.log("🔍 AppHeader store 状态:", {
  isDark: themeStore.isDark,
  toggleTheme: typeof themeStore.toggleTheme,
  monitorStatus: monitorStore.isRunning,
});
</script>

<style scoped>
.app-logo {
  width: 2rem;
  height: 2rem;
  background-color: var(--n-primary-color);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon {
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.status-running {
  background-color: var(--n-success-color);
}

.status-stopped {
  background-color: var(--n-text-color-disabled);
}
</style>

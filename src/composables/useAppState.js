import { ref, computed, onMounted, onUnmounted } from "vue";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
import { createDiscreteApi } from "naive-ui";
import { isTauriEnv, safeInvoke, safeListen } from "../utils/tauri";

// 使用 Naive UI 的离散 API
const { message, dialog } = createDiscreteApi(["message", "dialog"]);

// 全局状态管理
export function useAppState() {
  // 基础状态
  const activeTab = ref("monitor");
  const isLoading = ref(false);
  const monitorStatus = ref(false);
  const monitorMessage = ref("");
  const monitorStats = ref({});
  const monitorData = ref([]);

  // 设置相关状态
  const settingsConfig = ref({
    interval_seconds: 30,
    display_limit: 100,
    notify_enabled: false,
  });
  const settingsKeywordsPriceRules = ref([]);
  const settingsExcludeKeywords = ref([]);
  const settingsLoginCookie = ref("");
  const settingsDingtalkHooks = ref([]);

  // 模态框状态
  const showKeywordsPriceModal = ref(false);
  const modalKeywordsPriceRules = ref([]);
  const showItemDetail = ref(false);
  const selectedItem = ref({});

  // 其他状态
  const isAutoSyncing = ref(false);
  const lastSyncTime = ref("");

  // 表格列配置
  const tableColumns = ref([
    {
      title: "时间",
      key: "time",
      width: 120,
      ellipsis: { tooltip: true },
    },
    {
      title: "标题",
      key: "title",
      width: 200,
      ellipsis: { tooltip: true },
    },
    {
      title: "价格",
      key: "price",
      width: 80,
      render: (rowData) => `¥${rowData.price}`,
    },
    {
      title: "卖家",
      key: "user_nick_name",
      width: 100,
      ellipsis: { tooltip: true },
    },
    {
      title: "地区",
      key: "area",
      width: 100,
      ellipsis: { tooltip: true },
    },
  ]);

  // 监控相关方法
  const startMonitor = async () => {
    try {
      isLoading.value = true;
      await safeInvoke("app_start_monitoring");
      message.success("监控启动成功");
    } catch (error) {
      console.error("启动监控失败:", error);
      message.error(`启动监控失败: ${error}`);
    } finally {
      isLoading.value = false;
    }
  };

  const stopMonitor = async () => {
    try {
      isLoading.value = true;
      await safeInvoke("app_stop_monitoring");
      message.success("监控已停止");
    } catch (error) {
      console.error("停止监控失败:", error);
      message.error(`停止监控失败: ${error}`);
    } finally {
      isLoading.value = false;
    }
  };

  const checkMonitorStatus = async () => {
    try {
      const isRunning = await safeInvoke("app_is_running");
      monitorStatus.value = isRunning;
      monitorMessage.value = isRunning ? "监控运行中" : "监控已停止";
    } catch (error) {
      console.error("检查监控状态失败:", error);
      monitorStatus.value = false;
      monitorMessage.value = "监控已停止";
    }
  };

  const clearMonitorData = async () => {
    dialog.warning({
      title: "确认清空",
      content: "确定要清空当前显示的监控数据吗？（仅清空前端显示，不影响后端数据）",
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: () => {
        try {
          monitorData.value = [];
          message.success("前端监控数据显示已清空");
        } catch (error) {
          console.error("清空前端监控数据显示失败:", error);
          message.error("清空前端监控数据显示失败: " + String(error));
        }
      },
    });
  };

  // 设置相关方法
  const saveAllSettings = async () => {
    try {
      isLoading.value = true;
      const config = {
        ...settingsConfig.value,
        keywords_price_rules: settingsKeywordsPriceRules.value,
        exclude_keywords: settingsExcludeKeywords.value,
        login_cookie: settingsLoginCookie.value,
        dingtalk_hooks: settingsDingtalkHooks.value,
      };

      await safeInvoke("config_update", { config });
      message.success("设置已保存");
      lastSyncTime.value = new Date().toLocaleString();
    } catch (error) {
      console.error("保存设置失败:", error);
      message.error(`保存设置失败: ${error}`);
    } finally {
      isLoading.value = false;
    }
  };

  // 加载配置
  const loadConfig = async () => {
    try {
      const config = await safeInvoke("config_get");
      if (config) {
        settingsConfig.value = {
          interval_seconds: config.interval_seconds || 30,
          display_limit: config.display_limit || 100,
          notify_enabled: config.notify_enabled || false,
        };
        settingsKeywordsPriceRules.value = config.keywords_price_rules || [];
        settingsExcludeKeywords.value = config.exclude_keywords || [];
        settingsLoginCookie.value = config.login_cookie || "";
        settingsDingtalkHooks.value = config.dingtalk_hooks || [];
      }
    } catch (error) {
      console.error("加载配置失败:", error);
    }
  };

  // 初始化
  const initialize = async () => {
    await loadConfig();
    await checkMonitorStatus();

    // 注意：不再使用轮询检查状态，而是依赖事件驱动
    // 监控状态变化通过 monitor_status_changed 事件在 monitor store 中处理
  };

  return {
    // 状态
    activeTab,
    isLoading,
    monitorStatus,
    monitorMessage,
    monitorStats,
    monitorData,
    settingsConfig,
    settingsKeywordsPriceRules,
    settingsExcludeKeywords,
    settingsLoginCookie,
    settingsDingtalkHooks,
    showKeywordsPriceModal,
    modalKeywordsPriceRules,
    showItemDetail,
    selectedItem,
    isAutoSyncing,
    lastSyncTime,
    tableColumns,

    // 方法
    startMonitor,
    stopMonitor,
    checkMonitorStatus,
    clearMonitorData,
    saveAllSettings,
    loadConfig,
    initialize,
  };
}

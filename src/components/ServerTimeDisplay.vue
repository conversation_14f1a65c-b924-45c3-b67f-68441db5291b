<template>
  <div class="server-time-display">
    <div class="time-info">
      <div class="server-time">
        <n-icon size="16" color="#18a058">
          <TimeOutline />
        </n-icon>
        <span class="time-label">服务器时间:</span>
        <span class="time-value">{{ displayServerTime }}</span>
      </div>
      <div class="local-time">
        <n-icon size="16" color="#2080f0">
          <LocationOutline />
        </n-icon>
        <span class="time-label">本地时间:</span>
        <span class="time-value">{{ displayLocalTime }}</span>
      </div>
      <div v-if="timeOffset !== null" class="time-offset">
        <n-icon size="16" :color="offsetColor">
          <SwapHorizontalOutline />
        </n-icon>
        <span class="time-label">时间差:</span>
        <span class="time-value" :style="{ color: offsetColor }">
          {{ formatOffset(timeOffset) }}
        </span>
      </div>
    </div>
    <div v-if="!isSynced" class="sync-status">
      <n-icon size="14" color="#f0a020">
        <WarningOutline />
      </n-icon>
      <span class="sync-text">时间未同步</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import { NIcon } from "naive-ui";
import {
  TimeOutline,
  LocationOutline,
  SwapHorizontalOutline,
  WarningOutline,
} from "@vicons/ionicons5";
import { listen } from "@tauri-apps/api/event";
import { safeInvoke } from "../utils/tauri";

// 响应式数据
const serverTime = ref(null);
const localTime = ref(new Date());
const timeOffset = ref(null);
const isSynced = ref(false);

// 事件监听器引用
let timeSyncUnlisten = null;
let timeUpdateInterval = null;

// 计算属性
const displayServerTime = computed(() => {
  if (!serverTime.value) return "未同步";
  return formatTime(serverTime.value);
});

const displayLocalTime = computed(() => {
  return formatTime(localTime.value);
});

const offsetColor = computed(() => {
  if (timeOffset.value === null) return "#666";
  const absOffset = Math.abs(timeOffset.value);
  if (absOffset <= 5) return "#18a058"; // 绿色 - 时间差很小
  if (absOffset <= 30) return "#f0a020"; // 橙色 - 时间差中等
  return "#d03050"; // 红色 - 时间差较大
});

// 格式化时间显示
const formatTime = (date) => {
  if (!date) return "--:--:--";
  const d = new Date(date);
  return d.toLocaleTimeString("zh-CN", {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

// 格式化时间偏移
const formatOffset = (offsetSeconds) => {
  if (offsetSeconds === null) return "未知";

  const absOffset = Math.abs(offsetSeconds);
  const sign = offsetSeconds >= 0 ? "+" : "-";

  if (absOffset < 60) {
    return `${sign}${absOffset}秒`;
  } else if (absOffset < 3600) {
    const minutes = Math.floor(absOffset / 60);
    const seconds = absOffset % 60;
    return `${sign}${minutes}分${seconds}秒`;
  } else {
    const hours = Math.floor(absOffset / 3600);
    const minutes = Math.floor((absOffset % 3600) / 60);
    return `${sign}${hours}时${minutes}分`;
  }
};

// 更新本地时间和计算服务器时间
const updateTimes = () => {
  const now = new Date();
  localTime.value = now;

  // 如果有时间偏移，计算当前的服务器时间
  if (timeOffset.value !== null) {
    const serverTimestamp = now.getTime() + timeOffset.value * 1000;
    serverTime.value = new Date(serverTimestamp);
  }
};

// 获取时间信息
const fetchTimeInfo = async () => {
  try {
    const timeInfo = await safeInvoke("time_get_info");
    console.log("🕒 获取时间信息:", timeInfo);

    if (timeInfo.server_time) {
      serverTime.value = new Date(timeInfo.server_time);
    }

    timeOffset.value = timeInfo.offset_seconds;
    isSynced.value = timeInfo.is_synced;

    // 如果需要重新同步，可以在这里处理
    if (timeInfo.needs_resync) {
      console.log("⚠️ 时间同步已过期，需要重新同步");
    }
  } catch (error) {
    console.error("获取时间信息失败:", error);
  }
};

// 生命周期
onMounted(async () => {
  // 初始获取时间信息
  await fetchTimeInfo();

  // 监听时间同步事件
  timeSyncUnlisten = await listen("time_sync_updated", (event) => {
    console.log("📡 收到时间同步事件:", event.payload);
    const { server_time, offset_seconds } = event.payload;

    serverTime.value = new Date(server_time);
    timeOffset.value = offset_seconds;
    isSynced.value = true;
  });

  // 启动定时更新（每秒更新一次）
  timeUpdateInterval = setInterval(updateTimes, 1000);

  // 立即更新一次
  updateTimes();
});

onUnmounted(() => {
  // 清理事件监听器
  if (timeSyncUnlisten) {
    timeSyncUnlisten();
  }

  // 清理定时器
  if (timeUpdateInterval) {
    clearInterval(timeUpdateInterval);
  }
});
</script>

<style scoped>
.server-time-display {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.server-time,
.local-time,
.time-offset {
  display: flex;
  align-items: center;
  gap: 4px;
}

.time-label {
  color: #999;
  font-weight: 500;
}

.time-value {
  color: #fff;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-weight: 600;
}

.sync-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: rgba(240, 160, 32, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(240, 160, 32, 0.3);
}

.sync-text {
  color: #f0a020;
  font-size: 11px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .server-time-display {
    flex-direction: column;
    gap: 8px;
    padding: 6px 8px;
  }

  .time-info {
    flex-direction: column;
    gap: 6px;
    align-items: flex-start;
  }
}
</style>

<template>
  <div class="log-viewer">
    <!-- 精简控制栏 -->
    <div class="controls-bar">
      <n-space size="small" align="center">
        <n-select
          v-model:value="filterLevel"
          :options="levelOptions"
          placeholder="级别"
          clearable
          size="small"
          style="width: 80px"
        />
        <n-input
          v-model:value="searchQuery"
          placeholder="搜索日志..."
          clearable
          size="small"
          style="width: 200px"
        />
        <n-button-group size="small">
          <n-button @click="clearLogs">清空</n-button>
          <n-button @click="exportLogs">导出</n-button>
        </n-button-group>
      </n-space>
    </div>

    <!-- 日志表格 -->
    <div ref="logTableRef" class="log-table-container">
      <n-data-table
        :columns="logColumns"
        :data="filteredLogs"
        max-height="70vh"
        striped
        virtual-scroll
        :row-key="(row) => row.id || row.timestamp"
        size="small"
      >
        <template #empty>
          <n-empty description="暂无日志数据">
            <template #icon>
              <span style="font-size: 48px">📋</span>
            </template>
          </n-empty>
        </template>
      </n-data-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, h } from "vue";
import { createDiscreteApi } from "naive-ui";
import { safeInvoke, safeListen } from "../utils/tauri";

// 使用Naive UI的离散API
const { message, dialog } = createDiscreteApi(["message", "dialog"], {
  messageProviderProps: {
    keepAliveOnHover: true,
  },
});

// 响应式数据
const allLogs = ref([]);
const filteredLogs = ref([]);
const filterLevel = ref("");
const searchQuery = ref("");
const lastUpdateTime = ref(null);

// 表格动态高度相关
const logTableRef = ref(null);
const dynamicTableHeight = ref(400); // 默认高度

// 事件监听器
let unlistenLogEntry = null;

// 日志级别选项
const levelOptions = [
  { value: "", label: "全部" },
  { value: "DEBUG", label: "调试" },
  { value: "INFO", label: "信息" },
  { value: "WARN", label: "警告" },
  { value: "ERROR", label: "错误" },
];

// 表格列配置
const logColumns = [
  {
    title: "时间",
    key: "timestamp",
    width: 160,
    render: (rowData) => {
      return formatTime(rowData.timestamp);
    },
  },
  {
    title: "级别",
    key: "level",
    width: 80,
    render: (rowData) => {
      return h(
        "n-tag",
        {
          type: getLogLevelType(rowData.level),
          size: "small",
        },
        { default: () => rowData.level }
      );
    },
  },
  {
    title: "服务",
    key: "service",
    width: 120,
    ellipsis: {
      tooltip: false,
    },
  },
  {
    title: "操作",
    key: "action",
    width: 140,
    ellipsis: {
      tooltip: false,
    },
  },
  {
    title: "消息",
    key: "message",
    ellipsis: {
      tooltip: true,
    },
    render: (rowData) => {
      return h(
        "span",
        {
          style: {
            fontFamily: '"Courier New", monospace',
            fontSize: "13px",
            lineHeight: "1.4",
          },
        },
        rowData.message
      );
    },
  },
];

// 加载初始日志
async function loadInitialLogs() {
  try {
    const logs = await safeInvoke("logs_get", { limit: 1000 });
    allLogs.value = logs;
    filterLogs();
  } catch (error) {
    console.error("加载日志失败:", error);
    message.error("加载日志失败: " + error);
  }
}

// 设置实时监听
async function setupRealtimeListening() {
  try {
    unlistenLogEntry = await safeListen("new_log_entry", (event) => {
      const newLog = event.payload;
      allLogs.value.push(newLog);
      lastUpdateTime.value = Date.now();

      // 保持日志数量限制，避免内存过大
      if (allLogs.value.length > 2000) {
        allLogs.value = allLogs.value.slice(-1500);
      }

      filterLogs();
    });
  } catch (error) {
    console.error("设置实时日志监听失败:", error);
    message.error("设置实时日志监听失败: " + error);
  }
}

// 过滤日志
function filterLogs() {
  let filtered = [...allLogs.value]; // 创建副本避免修改原数组

  // 按级别过滤
  if (filterLevel.value) {
    filtered = filtered.filter((log) => log.level === filterLevel.value);
  }

  // 按搜索关键词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(
      (log) =>
        log.message.toLowerCase().includes(query) ||
        log.service.toLowerCase().includes(query) ||
        log.action.toLowerCase().includes(query)
    );
  }

  // 倒序排列，最新的日志在上面
  filteredLogs.value = filtered.reverse();
}

// 清空日志（仅清空前端显示）
function clearLogs() {
  dialog.warning({
    title: "确认清空",
    content:
      "确定要清空当前显示的日志吗？（仅清空前端显示，不影响后端日志文件）",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      try {
        allLogs.value = [];
        filteredLogs.value = [];
        message.success("前端日志显示已清空");
      } catch (error) {
        console.error("清空前端日志显示失败:", error);
        message.error("清空前端日志显示失败: " + String(error));
      }
    },
  });
}

// 导出日志
async function exportLogs() {
  try {
    const result = await safeInvoke("logs_export");
    console.log("导出成功: " + result);
    message.success("导出成功: " + result);
  } catch (error) {
    console.error("导出日志失败:", error);
    message.error("导出日志失败: " + error);
  }
}

// 格式化时间
function formatTime(timestamp) {
  if (typeof timestamp === "number") {
    return new Date(timestamp).toLocaleString();
  }
  return timestamp;
}

// 获取日志级别的颜色类型
function getLogLevelType(level) {
  switch (level.toLowerCase()) {
    case "error":
      return "error";
    case "warn":
      return "warning";
    case "info":
      return "info";
    case "debug":
      return "default";
    default:
      return "default";
  }
}

// 监听过滤条件变化
watch([filterLevel, searchQuery], () => {
  filterLogs();
});

// 组件挂载
onMounted(async () => {
  await loadInitialLogs();
  await setupRealtimeListening();
});

// 组件卸载
onUnmounted(() => {
  if (unlistenLogEntry) {
    unlistenLogEntry();
  }
});
</script>

<style scoped>
.log-viewer {
  display: flex;
  flex-direction: column;
  height: 100%; /* 使用父容器的全部高度 */
  gap: 12px; /* 调整间距与外层卡片协调 */
}

.controls-bar {
  flex-shrink: 0;
  padding: 12px;
  background: var(--n-color-embedded);
  border-radius: 6px;
  margin-bottom: 12px;
}

.log-table-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex child能正确收缩 */
}

/* 表格样式优化 */
.log-table-container :deep(.n-data-table) {
  /* 移除固定高度，让max-height属性控制 */
  display: flex;
  flex-direction: column;
}

/* 表格行hover效果 */
.log-table-container :deep(.n-data-table-tbody .n-data-table-tr:hover) {
  background-color: var(--n-table-color-hover);
  transition: background-color 0.3s ease;
}

/* 时间戳列样式 */
.log-table-container :deep(.n-data-table-td:first-child) {
  font-family: "Courier New", monospace;
  font-size: 12px;
}

/* 服务和操作列样式 */
.log-table-container :deep(.n-data-table-td:nth-child(3)),
.log-table-container :deep(.n-data-table-td:nth-child(4)) {
  font-weight: 500;
}

/* 消息列已在render中设置样式 */

/* 响应式设计 */
@media (max-width: 768px) {
  .log-viewer {
    height: 100%; /* 移动端也使用父容器的全部高度 */
    gap: 12px;
  }

  /* 移动端表格优化 */
  .log-table-container :deep(.n-data-table) {
    font-size: 12px;
  }

  .log-table-container :deep(.n-data-table-th),
  .log-table-container :deep(.n-data-table-td) {
    padding: 8px 4px !important;
  }
}

@media (max-width: 480px) {
  /* 小屏幕进一步优化 */
  .log-table-container :deep(.n-data-table) {
    font-size: 11px;
  }

  .log-table-container :deep(.n-data-table-th),
  .log-table-container :deep(.n-data-table-td) {
    padding: 6px 2px !important;
  }
}

/* 滚动条美化 */
.log-table-container :deep(.n-scrollbar)::-webkit-scrollbar {
  width: 6px;
}

.log-table-container :deep(.n-scrollbar)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.log-table-container :deep(.n-scrollbar)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.log-table-container :deep(.n-scrollbar)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 
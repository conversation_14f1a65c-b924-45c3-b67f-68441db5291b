<template>
  <n-modal
    v-model:show="show"
    preset="dialog"
    :title="notification.title"
    :type="getModalType(notification.type)"
    :closable="true"
    :mask-closable="true"
  >
    <div class="error-notification-content">
      <!-- 错误图标 -->
      <div class="error-icon">
        <n-icon :size="48" :color="getIconColor(notification.type)">
          <AlertCircleOutline />
        </n-icon>
      </div>

      <!-- 错误消息 -->
      <div class="error-message">
        {{ notification.message }}
      </div>

      <!-- 额外提示信息 -->
      <div v-if="getExtraTips(notification.type)" class="error-tips">
        <n-alert :type="getAlertType(notification.type)" :show-icon="false">
          {{ getExtraTips(notification.type) }}
        </n-alert>
      </div>
    </div>
  </n-modal>
</template>

<script setup>
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useMonitorStore } from "../stores/monitor";
import { AlertCircleOutline } from "@vicons/ionicons5";

const router = useRouter();
const monitorStore = useMonitorStore();

// 计算属性
const show = computed({
  get: () => monitorStore.errorNotification.show,
  set: (value) => {
    if (!value) {
      monitorStore.hideErrorNotification();
    }
  },
});

const notification = computed(() => monitorStore.errorNotification);

// 方法
function getModalType(errorType) {
  switch (errorType) {
    case "login_required":
    case "verification_required":
      return "warning";
    case "network_error":
      return "error";
    case "activation_error":
      return "error";
    default:
      return "warning";
  }
}

function getIconColor(errorType) {
  switch (errorType) {
    case "login_required":
    case "verification_required":
      return "#f0a020";
    case "network_error":
    case "activation_error":
      return "#d03050";
    default:
      return "#f0a020";
  }
}

function getAlertType(errorType) {
  switch (errorType) {
    case "login_required":
    case "verification_required":
      return "warning";
    case "network_error":
    case "activation_error":
      return "error";
    default:
      return "info";
  }
}

function getExtraTips(errorType) {
  switch (errorType) {
    case "login_required":
      return "提示：登录后监控将自动恢复运行";
    case "verification_required":
      return "提示：完成验证后监控将自动恢复运行";
    case "network_error":
      return "提示：请检查网络连接状态，确保能正常访问互联网";
    case "activation_error":
      return "提示：软件需要有效的激活状态才能正常使用监控功能";
    default:
      return null;
  }
}

async function handleAction(action) {
  if (!action) return;

  switch (action.action) {
    case "goto_login":
      // 这里可以触发登录流程或跳转到登录页面
      console.log("前往登录");
      // 可以调用后端的登录命令或打开登录窗口
      break;

    case "goto_verification":
      // 这里可以触发验证码流程
      console.log("前往验证");
      // 可以调用后端的验证码处理命令
      break;

    case "goto_activation":
      // 跳转到激活页面
      console.log("前往激活");
      router.push("/activation");
      break;

    case "retry_monitor":
      // 重试启动监控
      console.log("重试监控");
      try {
        await monitorStore.startMonitor();
      } catch (error) {
        console.error("重试监控失败:", error);
      }
      break;

    case "dismiss":
    default:
      // 关闭弹窗
      break;
  }

  // 关闭弹窗
  monitorStore.hideErrorNotification();
}
</script>

<style scoped>
.error-notification-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
}

.error-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.error-message {
  text-align: center;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color-1);
}

.error-tips {
  width: 100%;
  margin-top: 8px;
}

.error-tips :deep(.n-alert) {
  font-size: 12px;
}
</style>

<template>
  <div class="debug-panel">
    <n-card title="🔧 调试信息面板" size="small">
      <n-space vertical>
        <!-- 平台信息 -->
        <n-card title="平台信息" size="small">
          <n-descriptions :column="2" size="small">
            <n-descriptions-item label="用户代理">{{
              debugInfo.userAgent
            }}</n-descriptions-item>
            <n-descriptions-item label="当前时间">{{
              debugInfo.currentTime
            }}</n-descriptions-item>
            <n-descriptions-item label="应用状态">{{
              appStore.appState
            }}</n-descriptions-item>
            <n-descriptions-item label="激活状态">{{
              appStore.isActivated ? "已激活" : "未激活"
            }}</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 前端调试日志 -->
        <n-card title="前端调试日志" size="small">
          <n-space vertical size="small">
            <n-space>
              <n-button size="small" @click="exportFrontendLogs"
                >导出前端日志</n-button
              >
              <n-button size="small" @click="clearFrontendLogs"
                >清空前端日志</n-button
              >
              <n-button size="small" @click="refreshLogs">刷新</n-button>
            </n-space>
            <div class="log-container">
              <div
                v-for="log in frontendLogs.slice(-20)"
                :key="log.timestamp"
                class="log-entry"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span
                  class="log-type"
                  :class="`log-${log.type.toLowerCase()}`"
                  >{{ log.type }}</span
                >
                <span class="log-message">{{ log.message }}</span>
                <span v-if="log.data && log.data !== '{}'" class="log-data">{{
                  log.data
                }}</span>
              </div>
            </div>
          </n-space>
        </n-card>

        <!-- 应用状态日志 -->
        <n-card title="应用状态日志" size="small">
          <n-space vertical size="small">
            <n-button size="small" @click="exportAppStoreLogs"
              >导出状态日志</n-button
            >
            <div class="log-container">
              <div
                v-for="log in appStoreLogs.slice(-15)"
                :key="log.timestamp"
                class="log-entry"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span
                  class="log-type"
                  :class="`log-${log.type.toLowerCase()}`"
                  >{{ log.type }}</span
                >
                <span class="log-message">{{ log.message }}</span>
                <span v-if="log.data && log.data !== '{}'" class="log-data">{{
                  log.data
                }}</span>
              </div>
            </div>
          </n-space>
        </n-card>

        <!-- 后端日志文件路径 -->
        <n-card title="后端日志" size="small">
          <n-space vertical size="small">
            <n-button size="small" @click="getLogFilePath"
              >获取日志文件路径</n-button
            >
            <n-button size="small" @click="openLogDirectory"
              >打开日志目录</n-button
            >
            <div v-if="logFilePath" class="log-path">
              <n-text code>{{ logFilePath }}</n-text>
            </div>
          </n-space>
        </n-card>

        <!-- 窗口调试工具 -->
        <n-card title="窗口调试" size="small">
          <n-space>
            <n-button size="small" @click="logWindowInfo"
              >记录窗口信息</n-button
            >
            <n-button size="small" @click="testWindowEvents"
              >测试窗口事件</n-button
            >
          </n-space>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useAppStore } from "../stores/app";
import { safeInvoke } from "../utils/tauri";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
import { createDiscreteApi } from "naive-ui";

// 使用Naive UI的离散API
const { message, dialog } = createDiscreteApi(["message", "dialog"]);

const appStore = useAppStore();

// 响应式数据
const frontendLogs = ref([]);
const appStoreLogs = ref([]);
const logFilePath = ref("");
const debugInfo = ref({
  userAgent: navigator.userAgent,
  currentTime: new Date().toISOString(),
});

// 计算属性
const currentWindow = computed(() => getCurrentWebviewWindow());

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString();
};

// 刷新日志
const refreshLogs = () => {
  try {
    frontendLogs.value = JSON.parse(
      localStorage.getItem("goldfish_debug_logs") || "[]"
    );
    appStoreLogs.value = JSON.parse(
      localStorage.getItem("goldfish_app_store_logs") || "[]"
    );
    debugInfo.value.currentTime = new Date().toISOString();
  } catch (e) {
    console.error("刷新日志失败:", e);
  }
};

// 导出前端日志
const exportFrontendLogs = () => {
  try {
    if (window.goldfishDebug && window.goldfishDebug.exportLogs) {
      window.goldfishDebug.exportLogs();
    } else {
      const logs = JSON.parse(
        localStorage.getItem("goldfish_debug_logs") || "[]"
      );
      const blob = new Blob([JSON.stringify(logs, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `goldfish_frontend_debug_${new Date()
        .toISOString()
        .replace(/[:.]/g, "-")}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  } catch (e) {
    console.error("导出前端日志失败:", e);
  }
};

// 导出应用状态日志
const exportAppStoreLogs = () => {
  try {
    const logs = JSON.parse(
      localStorage.getItem("goldfish_app_store_logs") || "[]"
    );
    const blob = new Blob([JSON.stringify(logs, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `goldfish_app_store_debug_${new Date()
      .toISOString()
      .replace(/[:.]/g, "-")}.json`;
    a.click();
    URL.revokeObjectURL(url);
  } catch (e) {
    console.error("导出应用状态日志失败:", e);
  }
};

// 清空前端日志
const clearFrontendLogs = () => {
  dialog.warning({
    title: "确认清空",
    content: "确定要清空所有前端调试日志吗？此操作不可撤销。",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      try {
        localStorage.removeItem("goldfish_debug_logs");
        localStorage.removeItem("goldfish_app_store_logs");
        frontendLogs.value = [];
        appStoreLogs.value = [];
        message.success("前端日志已清空");
      } catch (e) {
        console.error("清空前端日志失败:", e);
        message.error("清空前端日志失败: " + String(e));
      }
    },
  });
};

// 获取日志文件路径
const getLogFilePath = async () => {
  try {
    const path = await safeInvoke("logs_get_file_path");
    logFilePath.value = path;
  } catch (e) {
    console.error("获取日志文件路径失败:", e);
    message.error("获取日志文件路径失败: " + String(e));
  }
};

// 打开日志目录
const openLogDirectory = async () => {
  try {
    await safeInvoke("logs_open_directory");
  } catch (e) {
    console.error("打开日志目录失败:", e);
  }
};

// 记录窗口信息
const logWindowInfo = async () => {
  try {
    const window = currentWindow.value;
    const windowInfo = {
      label: window.label,
      isVisible: await window.isVisible(),
      isMinimized: await window.isMinimized(),
      isMaximized: await window.isMaximized(),
      isFocused: await window.isFocused(),
      innerSize: await window.innerSize(),
      outerSize: await window.outerSize(),
      innerPosition: await window.innerPosition(),
      outerPosition: await window.outerPosition(),
    };

    console.log("🪟 窗口信息:", windowInfo);

    // 保存到调试日志
    const logEntry = {
      timestamp: new Date().toISOString(),
      type: "WINDOW_INFO",
      message: "窗口信息记录",
      data: JSON.stringify(windowInfo),
    };

    const existingLogs = JSON.parse(
      localStorage.getItem("goldfish_debug_logs") || "[]"
    );
    existingLogs.push(logEntry);
    localStorage.setItem("goldfish_debug_logs", JSON.stringify(existingLogs));

    refreshLogs();
  } catch (e) {
    console.error("记录窗口信息失败:", e);
  }
};

// 测试窗口事件
const testWindowEvents = () => {
  console.log("🧪 测试窗口事件");

  // 记录当前状态
  const testLog = {
    timestamp: new Date().toISOString(),
    type: "WINDOW_TEST",
    message: "窗口事件测试",
    data: JSON.stringify({
      appState: appStore.appState,
      isActivated: appStore.isActivated,
      currentRoute: window.location.hash,
    }),
  };

  const existingLogs = JSON.parse(
    localStorage.getItem("goldfish_debug_logs") || "[]"
  );
  existingLogs.push(testLog);
  localStorage.setItem("goldfish_debug_logs", JSON.stringify(existingLogs));

  refreshLogs();
};

// 页面加载时初始化
onMounted(() => {
  refreshLogs();
  getLogFilePath();
});
</script>

<style scoped>
.debug-panel {
  padding: 16px;
  max-height: 80vh;
  overflow-y: auto;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: var(--n-color-embedded);
  border-radius: 4px;
  padding: 8px;
  font-family: "Courier New", monospace;
  font-size: 12px;
}

.log-entry {
  display: block;
  margin-bottom: 4px;
  line-height: 1.4;
}

.log-time {
  color: var(--n-text-color-disabled);
  margin-right: 8px;
}

.log-type {
  font-weight: bold;
  margin-right: 8px;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 10px;
}

.log-mount {
  background: var(--n-info-color-suppl);
  color: var(--n-info-color);
}
.log-state_change {
  background: var(--n-warning-color-suppl);
  color: var(--n-warning-color);
}
.log-route_decision {
  background: var(--n-success-color-suppl);
  color: var(--n-success-color);
}
.log-error {
  background: var(--n-error-color-suppl);
  color: var(--n-error-color);
}
.log-message {
  margin-right: 8px;
}

.log-data {
  font-size: 11px;
  word-break: break-all;
}

.log-path {
  padding: 8px;
  border-radius: 4px;
  word-break: break-all;
}
</style>

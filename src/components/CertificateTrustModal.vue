<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="🔒 SSL证书信任检测"
    :mask-closable="false"
    :closable="true"
    style="width: 700px; max-height: 80vh"
    :on-close="handleClose"
  >
    <div class="certificate-modal-content">
      <!-- 检测进行中 -->
      <div v-if="isChecking" class="checking-status">
        <n-spin size="large">
          <template #description>
            <span>正在检测SSL证书信任状态...</span>
          </template>
        </n-spin>
      </div>

      <!-- 检测结果 -->
      <div v-else-if="certificateStatus" class="check-results">
        <!-- 整体状态 -->
        <n-alert
          :type="alertType"
          :title="alertTitle"
          style="margin-bottom: 16px"
        >
          {{ certificateStatus.recommendation }}
        </n-alert>

        <!-- 中间人代理检测结果 -->
        <div v-if="certificateStatus.mitm_proxy_detected" class="mitm-warning">
          <n-alert
            type="warning"
            title="⚠️ 检测到中间人代理"
            style="margin-bottom: 16px"
          >
            系统检测到可能的中间人代理，这可能影响应用的正常使用。请按照下方指导进行操作。
          </n-alert>
        </div>

        <!-- 详细检测结果 -->
        <n-collapse style="margin-bottom: 16px">
          <n-collapse-item title="查看详细检测结果" name="details">
            <n-data-table
              :columns="resultColumns"
              :data="certificateStatus.test_results"
              :pagination="false"
              size="small"
            />
          </n-collapse-item>
        </n-collapse>

        <!-- 修复指导 -->
        <div v-if="fixGuidance" class="fix-guidance">
          <h4>🔧 修复指导</h4>
          <div class="guidance-content">
            <pre>{{ fixGuidance }}</pre>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <n-space>
            <n-button @click="recheckCertificates" :loading="isRechecking">
              重新检测
            </n-button>
            <n-button
              type="primary"
              @click="openCertificateManager"
              v-if="certificateStatus.mitm_proxy_detected"
            >
              打开证书管理器
            </n-button>
            <n-button @click="handleClose"> 关闭 </n-button>
          </n-space>
        </div>
      </div>

      <!-- 检测失败 -->
      <div v-else class="check-error">
        <n-alert type="error" title="检测失败">
          无法完成SSL证书信任状态检测，请检查网络连接后重试。
        </n-alert>
        <div class="action-buttons" style="margin-top: 16px">
          <n-space>
            <n-button type="primary" @click="recheckCertificates">
              重试
            </n-button>
            <n-button @click="handleClose"> 关闭 </n-button>
          </n-space>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { invoke } from "@tauri-apps/api/core";
import {
  NModal,
  NAlert,
  NSpin,
  NButton,
  NSpace,
  NCollapse,
  NCollapseItem,
  NDataTable,
} from "naive-ui";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["close", "certificate-status-changed"]);

const showModal = ref(false);
const isChecking = ref(false);
const isRechecking = ref(false);
const certificateStatus = ref(null);
const fixGuidance = ref("");

// 监听props变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      showModal.value = true;
      checkCertificates();
    }
  },
  { immediate: true }
);

// 计算属性
const alertType = computed(() => {
  if (!certificateStatus.value) return "info";
  if (certificateStatus.value.mitm_proxy_detected) return "error";
  if (!certificateStatus.value.overall_trusted) return "warning";
  return "success";
});

const alertTitle = computed(() => {
  if (!certificateStatus.value) return "检测中";
  if (certificateStatus.value.mitm_proxy_detected) return "🚨 发现中间人代理";
  if (!certificateStatus.value.overall_trusted) return "⚠️ 证书验证异常";
  return "✅ 证书验证正常";
});

// 表格列定义
const resultColumns = [
  {
    title: "检测网站",
    key: "check_url",
    width: 200,
  },
  {
    title: "信任状态",
    key: "is_trusted",
    width: 100,
    render(row) {
      return row.is_trusted ? "✅ 信任" : "❌ 不信任";
    },
  },
  {
    title: "中间人检测",
    key: "is_mitm_detected",
    width: 120,
    render(row) {
      return row.is_mitm_detected ? "⚠️ 检测到" : "✅ 正常";
    },
  },
  {
    title: "响应时间",
    key: "response_time_ms",
    width: 100,
    render(row) {
      return row.response_time_ms ? `${row.response_time_ms}ms` : "-";
    },
  },
  {
    title: "错误信息",
    key: "error_message",
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return row.error_message || "-";
    },
  },
];

// 移除checkCertificates、recheckCertificates、openCertificateManager等与证书检测相关的方法和UI

// 处理关闭
const handleClose = () => {
  showModal.value = false;
  emit("close");
};
</script>

<style scoped>
.certificate-modal-content {
  max-height: 70vh;
  overflow-y: auto;
}

.checking-status {
  text-align: center;
  padding: 40px 20px;
}

.check-results,
.check-error {
  min-height: 200px;
}

.mitm-warning {
  margin-bottom: 16px;
}

.fix-guidance {
  margin-bottom: 20px;
}

.fix-guidance h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-weight: 600;
}

.guidance-content {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.guidance-content pre {
  margin: 0;
  font-family: inherit;
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.6;
  color: #495057;
}

.action-buttons {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

/* 滚动条样式 */
.certificate-modal-content::-webkit-scrollbar {
  width: 6px;
}

.certificate-modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.certificate-modal-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.certificate-modal-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 
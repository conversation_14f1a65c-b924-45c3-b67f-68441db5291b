/**
 * Tauri环境检测和兼容性工具
 */

// 检测是否在Tauri环境中
export const isTauriEnv = () => {
  // 检查多个可能的Tauri标识
  if (typeof window === 'undefined') return false

  // 检查 __TAURI__ 全局对象
  if (window.__TAURI__ !== undefined) return true

  // 检查 Tauri API 是否可用
  if (window.__TAURI_INTERNALS__ !== undefined) return true

  // 检查是否在 Tauri 应用中（通过 user agent 或其他标识）
  if (navigator.userAgent && navigator.userAgent.includes('Tauri')) return true

  // 检查是否有 tauri:// 协议支持
  try {
    return window.location.protocol === 'tauri:' ||
      window.location.protocol === 'https:' && window.location.hostname === 'tauri.localhost'
  } catch (e) {
    return false
  }
}

// 检测是否在开发环境
export const isDev = () => {
  return import.meta.env.DEV
}

// 安全调用Tauri API的包装器
export const safeTauriCall = async (apiCall, fallbackValue = null, errorMessage = 'Tauri API调用失败') => {
  if (!isTauriEnv()) {
    console.warn('⚠️ 非Tauri环境，跳过API调用:', errorMessage)
    return fallbackValue
  }

  try {
    return await apiCall()
  } catch (error) {
    console.error(errorMessage, error)
    return fallbackValue
  }
}

// 安全调用Tauri invoke命令
export const safeInvoke = async (command, args = {}, fallbackValue = null) => {
  if (!isTauriEnv()) {
    console.warn(`⚠️ 非Tauri环境，跳过invoke调用: ${command}`)
    return fallbackValue
  }

  try {
    const { invoke } = await import('@tauri-apps/api/core')
    return await invoke(command, args)
  } catch (error) {
    console.error(`Tauri invoke调用失败: ${command}`, error)
    // 抛出错误而不是返回fallbackValue，这样前端可以正确处理错误
    throw new Error(error.toString())
  }
}

// 安全调用Tauri事件监听
export const safeListen = async (event, handler) => {
  if (!isTauriEnv()) {
    console.warn(`⚠️ 非Tauri环境，跳过事件监听: ${event}`)
    return () => { } // 返回空的取消监听函数
  }

  try {
    const { listen } = await import('@tauri-apps/api/event')
    return await listen(event, handler)
  } catch (error) {
    console.error(`Tauri事件监听失败: ${event}`, error)
    return () => { }
  }
}

// 模拟数据生成器（用于浏览器环境调试）
export const mockData = {
  // 模拟配置数据
  config: {
    keywords: ['iPhone', 'MacBook', '显卡'],
    dingtalk_hooks: ['https://oapi.dingtalk.com/robot/send?access_token=mock'],
    login_cookie: 'mock_cookie_value',
    polling_interval: 30,
    rsa_activation_info: {
      activation_code: 'MOCK-ACTIVATION-CODE',
      device_id: 'mock-device-id'
    }
  },

  // 模拟监控数据
  monitorData: [
    {
      id: '1',
      title: 'iPhone 15 Pro Max 256GB',
      price: '8999',
      original_price: '9999',
      location: '北京',
      seller: '张三',
      item_id: '123456789',
      keyword: 'iPhone',
      timestamp: new Date().toISOString(),
      url: 'https://www.goofish.com/item?id=123456789'
    },
    {
      id: '2',
      title: 'MacBook Pro M3 16寸',
      price: '15999',
      original_price: '18999',
      location: '上海',
      seller: '李四',
      item_id: '987654321',
      keyword: 'MacBook',
      timestamp: new Date().toISOString(),
      url: 'https://www.goofish.com/item?id=987654321'
    }
  ],

  // 模拟日志数据
  logs: [
    {
      id: '1',
      timestamp: new Date().toISOString(),
      level: 'INFO',
      service: 'MonitorService',
      action: 'start_monitoring',
      message: '开始监控关键词: iPhone'
    },
    {
      id: '2',
      timestamp: new Date().toISOString(),
      level: 'SUCCESS',
      service: 'MonitorService',
      action: 'found_item',
      message: '发现新商品: iPhone 15 Pro Max'
    }
  ],

  // 模拟监控状态
  monitorStatus: {
    is_running: false,
    items_count: 2,
    total_found_items: 15,
    execution_count: 5,
    total_keywords: 3,
    uptime_seconds: 0,
    last_execution: new Date().toISOString()
  }
}

// 开发环境下的调试工具
export const devTools = {
  // 在控制台暴露调试工具
  expose: () => {
    if (isDev() && typeof window !== 'undefined') {
      window.goldfishDevTools = {
        isTauriEnv: isTauriEnv(),
        mockData,
        safeTauriCall,
        safeInvoke,
        safeListen,
        // 调试工具
        debug: {
          checkEnv: () => {
            console.log('🔍 Tauri环境检测结果:', isTauriEnv())
            console.log('🔍 开发环境:', isDev())
            console.log('🔍 window.__TAURI__:', typeof window !== 'undefined' ? window.__TAURI__ : 'window未定义')
            console.log('🔍 window.__TAURI_INTERNALS__:', typeof window !== 'undefined' ? window.__TAURI_INTERNALS__ : 'window未定义')
            console.log('🔍 location.protocol:', typeof window !== 'undefined' ? window.location.protocol : 'window未定义')
            console.log('🔍 navigator.userAgent:', typeof navigator !== 'undefined' ? navigator.userAgent : 'navigator未定义')
          },
          // 强制测试 invoke 调用
          testInvoke: async (command = 'app_get_status', args = {}) => {
            console.log(`🧪 强制测试 invoke 调用: ${command}`)
            try {
              const { invoke } = await import('@tauri-apps/api/core')
              const result = await invoke(command, args)
              console.log(`✅ 强制 invoke 成功:`, result)
              return result
            } catch (error) {
              console.error(`❌ 强制 invoke 失败:`, error)
              return null
            }
          }
        }
      }
      console.log('🔧 开发工具已暴露到 window.goldfishDevTools')
      console.log('🔧 使用 window.goldfishDevTools.debug.checkEnv() 检查环境')
      console.log('🔧 使用 window.goldfishDevTools.debug.testInvoke() 测试强制调用')
    }
  }
}

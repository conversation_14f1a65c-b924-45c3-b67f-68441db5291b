import { createApp } from "vue";
import Root from "./Root.vue";
import router from "./router";
import "./assets/tailwind.css";

// 导入 Naive UI
import naive from 'naive-ui'

// 导入 Pinia
import { createPinia } from 'pinia'

// 导入开发工具
import { devTools } from "./utils/tauri";

const app = createApp(Root);
const pinia = createPinia()

// 使用路由、Pinia 和 Naive UI
app.use(pinia);
app.use(router);
app.use(naive);

// 在开发环境下暴露调试工具
devTools.expose();

app.mount("#app");

{"name": "goldfish-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "build:windows": "npm run build && npm run tauri build -- --target x86_64-pc-windows-gnu", "build:windows-msvc": "npm run build && npm run tauri build -- --target x86_64-pc-windows-msvc", "build:macos": "npm run build && npm run tauri build", "activation-generator": "python examples/activation_generator/activation_generator.py"}, "dependencies": {"@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-opener": "^2", "@vicons/ionicons5": "^0.13.0", "@vicons/material": "^0.13.0", "naive-ui": "^2.42.0", "pinia": "^3.0.3", "qrcode": "^1.5.4", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^6.0.3"}}
{"rust-analyzer.checkOnSave.command": "cargo check", "rust-analyzer.procMacro.enable": false, "rust-analyzer.cargo.buildScripts.enable": true, "files.watcherExclude": {"**/target/**": true, "**/node_modules/**": true}, "rust-analyzer.checkOnSave.enable": true, "rust-analyzer.checkOnSave.allTargets": false, "rust-analyzer.cargo.target": null, "rust-analyzer.debug.engine": "vadimcn.vscode-lldb", "rust-analyzer.debug.openDebugPane": true, "rust-analyzer.debug.sourceFileMap": {"/rustc/*": null}, "lldb.verboseLogging": true, "lldb.dereferencePointers": true, "lldb.showDisassembly": "never", "lldb.displayFormat": "auto", "lldb.suppressMissingSourceFiles": false, "debug.allowBreakpointsEverywhere": true, "debug.inlineValues": "on", "debug.showInlineBreakpointCandidates": true, "debug.showBreakpointsInOverviewRuler": true}
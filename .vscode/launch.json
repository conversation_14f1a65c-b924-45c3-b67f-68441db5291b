{"version": "0.2.0", "configurations": [{"type": "lldb", "request": "launch", "name": "Debug with <PERSON><PERSON> (Recommended)", "program": "${workspaceFolder}/src-tauri/target/debug/goldfish-app", "args": [], "cwd": "${workspaceFolder}", "stopOnEntry": false, "environment": [{"name": "RUST_BACKTRACE", "value": "1"}, {"name": "RUST_LOG", "value": "goldfish_app=info,tao=error,reqwest=error,warn,error"}], "externalConsole": false, "sourceLanguages": ["rust"], "preLaunchTask": "tauri dev (recommended)", "console": "integratedTerminal", "presentation": {"hidden": false, "group": "", "order": 1}, "problemMatcher": ["$rustc"]}, {"type": "lldb", "request": "launch", "name": "Debug <PERSON><PERSON> (Full Stack)", "program": "${workspaceFolder}/src-tauri/target/debug/goldfish-app", "args": [], "cwd": "${workspaceFolder}", "stopOnEntry": false, "environment": [{"name": "RUST_BACKTRACE", "value": "1"}, {"name": "RUST_LOG", "value": "goldfish_app=info,tao=error,reqwest=error,warn,error"}], "externalConsole": false, "sourceLanguages": ["rust"], "preLaunchTask": "build with frontend", "console": "integratedTerminal", "presentation": {"hidden": false, "group": "", "order": 2}, "problemMatcher": ["$rustc"]}, {"type": "lldb", "request": "launch", "name": "Debug <PERSON><PERSON> Backend Only", "program": "${workspaceFolder}/src-tauri/target/debug/goldfish-app", "args": [], "cwd": "${workspaceFolder}", "stopOnEntry": false, "environment": [{"name": "RUST_BACKTRACE", "value": "1"}, {"name": "RUST_LOG", "value": "goldfish_app=info,tao=error,reqwest=error,warn,error"}], "externalConsole": false, "sourceLanguages": ["rust"], "preLaunchTask": "cargo build", "console": "integratedTerminal", "presentation": {"hidden": false, "group": "", "order": 3}, "problemMatcher": ["$rustc"]}, {"type": "cppvsdbg", "request": "launch", "name": "Debug Tauri App (Windows)", "program": "${workspaceFolder}/src-tauri/target/debug/goldfish-app.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "integratedTerminal"}, {"type": "cppdbg", "request": "launch", "name": "Debug Tauri App (GDB)", "program": "${workspaceFolder}/src-tauri/target/debug/goldfish-app", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}], "compounds": [{"name": "Launch Full Stack Debug", "configurations": ["Debug <PERSON><PERSON> (Full Stack)"], "stopAll": true, "presentation": {"hidden": false, "group": "", "order": 1}}]}
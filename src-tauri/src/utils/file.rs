use std::fs;
use std::path::Path;

/// 确保目录存在
pub fn ensure_dir_exists<P: AsRef<Path>>(path: P) -> Result<(), String> {
    fs::create_dir_all(path).map_err(|e| format!("创建目录失败: {}", e))
}

/// 检查文件是否存在
pub fn file_exists<P: AsRef<Path>>(path: P) -> bool {
    path.as_ref().exists()
}

/// 获取文件大小
pub fn get_file_size<P: AsRef<Path>>(path: P) -> Result<u64, String> {
    fs::metadata(path)
        .map(|metadata| metadata.len())
        .map_err(|e| format!("获取文件大小失败: {}", e))
}

/// 读取文件内容为字符串
pub fn read_file_to_string<P: AsRef<Path>>(path: P) -> Result<String, String> {
    fs::read_to_string(path).map_err(|e| format!("读取文件失败: {}", e))
}

/// 写入字符串到文件
pub fn write_string_to_file<P: AsRef<Path>>(path: P, content: &str) -> Result<(), String> {
    fs::write(path, content).map_err(|e| format!("写入文件失败: {}", e))
}

/// 复制文件
pub fn copy_file<P: AsRef<Path>>(from: P, to: P) -> Result<(), String> {
    fs::copy(&from, &to)
        .map(|_| ())
        .map_err(|e| format!("复制文件失败: {}", e))
}

/// 删除文件
pub fn delete_file<P: AsRef<Path>>(path: P) -> Result<(), String> {
    fs::remove_file(path).map_err(|e| format!("删除文件失败: {}", e))
}

/// 获取文件扩展名
pub fn get_file_extension<P: AsRef<Path>>(path: P) -> Option<String> {
    path.as_ref()
        .extension()
        .and_then(|ext| ext.to_str())
        .map(|s| s.to_string())
}

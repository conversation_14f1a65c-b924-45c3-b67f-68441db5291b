use chrono::{DateTime, Utc};
use std::time::{SystemTime, UNIX_EPOCH};

/// 获取当前UTC时间
pub fn current_utc_time() -> DateTime<Utc> {
    Utc::now()
}

/// 获取当前时间戳（秒）
pub fn current_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs()
}

/// 获取当前时间戳（毫秒）
pub fn current_timestamp_millis() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64
}

/// 时间戳转换为DateTime
pub fn timestamp_to_datetime(timestamp: i64) -> Option<DateTime<Utc>> {
    DateTime::from_timestamp(timestamp, 0)
}

/// 格式化时间
pub fn format_time(datetime: &DateTime<Utc>, format: &str) -> String {
    datetime.format(format).to_string()
}

/// 获取友好的时间显示
pub fn friendly_time_display(datetime: &DateTime<Utc>) -> String {
    let now = Utc::now();
    let duration = now.signed_duration_since(*datetime);
    
    if duration.num_seconds() < 60 {
        "刚刚".to_string()
    } else if duration.num_minutes() < 60 {
        format!("{}分钟前", duration.num_minutes())
    } else if duration.num_hours() < 24 {
        format!("{}小时前", duration.num_hours())
    } else if duration.num_days() < 30 {
        format!("{}天前", duration.num_days())
    } else {
        datetime.format("%Y-%m-%d %H:%M:%S").to_string()
    }
}

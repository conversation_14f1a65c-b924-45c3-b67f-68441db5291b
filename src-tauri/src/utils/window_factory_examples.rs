use crate::utils::{WindowFactory, WindowParams, WindowConfig};
use tauri::{AppHand<PERSON>, WebviewWindow};

/// 窗口工厂使用示例
pub struct WindowFactoryExamples;

impl WindowFactoryExamples {
    /// 示例1：创建简单的浏览器窗口
    pub fn create_simple_browser_window(
        app_handle: &AppHandle,
        url: &str,
    ) -> Result<WebviewWindow, String> {
        // 最简单的用法：只传URL
        WindowFactory::create_window(app_handle, url, None, None)
    }

    /// 示例2：创建带标题的窗口
    pub fn create_window_with_title(
        app_handle: &AppHandle,
        url: &str,
        title: &str,
    ) -> Result<WebviewWindow, String> {
        let params = WindowParams::new().with_title(title);
        
        WindowFactory::create_window(app_handle, url, Some(params), None)
    }

    /// 示例3：创建小窗口
    pub fn create_small_window(
        app_handle: &AppHandle,
        url: &str,
        title: &str,
    ) -> Result<WebviewWindow, String> {
        let params = WindowParams::new()
            .with_title(title)
            .with_config(WindowConfig::small());
        
        WindowFactory::create_window(app_handle, url, Some(params), None)
    }

    /// 示例4：创建自定义尺寸的窗口
    pub fn create_custom_size_window(
        app_handle: &AppHandle,
        url: &str,
        title: &str,
        width: f64,
        height: f64,
    ) -> Result<WebviewWindow, String> {
        let config = WindowConfig::default().with_size(width, height);
        let params = WindowParams::new()
            .with_title(title)
            .with_config(config);
        
        WindowFactory::create_window(app_handle, url, Some(params), None)
    }

    /// 示例5：创建带存储隔离的窗口
    pub fn create_isolated_window(
        app_handle: &AppHandle,
        url: &str,
        title: &str,
        session_id: &str,
    ) -> Result<WebviewWindow, String> {
        let params = WindowParams::new().with_title(title);
        
        WindowFactory::create_window(app_handle, url, Some(params), Some(session_id))
    }

    /// 示例6：创建带关闭回调的窗口
    pub fn create_window_with_callback(
        app_handle: &AppHandle,
        url: &str,
        title: &str,
    ) -> Result<WebviewWindow, String> {
        let params = WindowParams::new()
            .with_title(title)
            .with_callback(|window, event| {
                match event {
                    tauri::WindowEvent::CloseRequested { .. } => {
                        println!("🔄 窗口 {} 请求关闭", window.label());
                        // 在这里处理关闭逻辑
                    }
                    tauri::WindowEvent::Destroyed => {
                        println!("🗑️ 窗口 {} 已销毁", window.label());
                        // 在这里处理销毁后的清理逻辑
                    }
                    _ => {}
                }
            });
        
        WindowFactory::create_window(app_handle, url, Some(params), None)
    }

    /// 示例7：创建全屏窗口
    pub fn create_fullscreen_window(
        app_handle: &AppHandle,
        url: &str,
        title: &str,
    ) -> Result<WebviewWindow, String> {
        let params = WindowParams::new()
            .with_title(title)
            .with_config(WindowConfig::fullscreen());
        
        WindowFactory::create_window(app_handle, url, Some(params), None)
    }

    /// 示例8：创建无装饰窗口
    pub fn create_borderless_window(
        app_handle: &AppHandle,
        url: &str,
        title: &str,
    ) -> Result<WebviewWindow, String> {
        let config = WindowConfig::default()
            .with_size(800.0, 600.0);
        
        let mut config = config;
        config.decorations = Some(false);
        config.resizable = Some(false);
        
        let params = WindowParams::new()
            .with_title(title)
            .with_config(config);
        
        WindowFactory::create_window(app_handle, url, Some(params), None)
    }

    /// 示例9：创建置顶窗口
    pub fn create_always_on_top_window(
        app_handle: &AppHandle,
        url: &str,
        title: &str,
    ) -> Result<WebviewWindow, String> {
        let mut config = WindowConfig::small();
        config.always_on_top = Some(true);
        
        let params = WindowParams::new()
            .with_title(title)
            .with_config(config);
        
        WindowFactory::create_window(app_handle, url, Some(params), None)
    }

    /// 示例10：创建带最小/最大尺寸限制的窗口
    pub fn create_size_constrained_window(
        app_handle: &AppHandle,
        url: &str,
        title: &str,
    ) -> Result<WebviewWindow, String> {
        let config = WindowConfig::default()
            .with_size(1000.0, 700.0)
            .with_min_size(600.0, 400.0)
            .with_max_size(1600.0, 1200.0);
        
        let params = WindowParams::new()
            .with_title(title)
            .with_config(config);
        
        WindowFactory::create_window(app_handle, url, Some(params), None)
    }

    /// 示例11：创建指定位置的窗口
    pub fn create_positioned_window(
        app_handle: &AppHandle,
        url: &str,
        title: &str,
        x: f64,
        y: f64,
    ) -> Result<WebviewWindow, String> {
        let config = WindowConfig::default()
            .with_size(800.0, 600.0)
            .with_position(x, y);
        
        let mut config = config;
        config.center = Some(false); // 禁用居中，使用指定位置
        
        let params = WindowParams::new()
            .with_title(title)
            .with_config(config);
        
        WindowFactory::create_window(app_handle, url, Some(params), None)
    }

    /// 示例12：创建登录窗口（带Cookie提取回调）
    pub fn create_login_window(
        app_handle: &AppHandle,
        url: &str,
        session_id: &str,
    ) -> Result<WebviewWindow, String> {
        let params = WindowParams::new()
            .with_title("登录窗口")
            .with_config(WindowConfig::default().with_size(900.0, 700.0))
            .with_callback(move |window, event| {
                match event {
                    tauri::WindowEvent::CloseRequested { api, .. } => {
                        println!("🔄 登录窗口关闭，开始提取Cookie");
                        
                        // 阻止默认关闭，先处理Cookie
                        api.prevent_close();
                        
                        // 异步处理Cookie提取
                        let window = window.clone();
                        tauri::async_runtime::spawn(async move {
                            // 提取Cookie的逻辑
                            match window.cookies() {
                                Ok(cookies) => {
                                    println!("✅ 成功提取了 {} 个Cookie", cookies.len());
                                    // 处理Cookie...
                                }
                                Err(e) => {
                                    println!("❌ Cookie提取失败: {}", e);
                                }
                            }
                            
                            // 处理完成后关闭窗口
                            if let Err(e) = window.close() {
                                println!("❌ 关闭窗口失败: {}", e);
                            }
                        });
                    }
                    _ => {}
                }
            });
        
        WindowFactory::create_window(app_handle, url, Some(params), Some(session_id))
    }

    /// 示例13：创建验证窗口
    pub fn create_verification_window(
        app_handle: &AppHandle,
        url: &str,
        account_id: &str,
    ) -> Result<WebviewWindow, String> {
        let label = format!("verification_{}", account_id);
        
        let params = WindowParams::new()
            .with_title("账号验证")
            .with_config(WindowConfig::default().with_size(800.0, 600.0))
            .with_callback(move |window, event| {
                match event {
                    tauri::WindowEvent::CloseRequested { .. } => {
                        println!("🔄 验证窗口关闭，处理验证完成");
                        // 处理验证完成的逻辑
                    }
                    _ => {}
                }
            });
        
        WindowFactory::create_window_with_label(app_handle, &label, url, Some(params), Some(account_id))
    }
}

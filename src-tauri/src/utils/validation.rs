// 简化实现，不使用regex依赖

/// 验证邮箱格式（简化实现）
pub fn is_valid_email(email: &str) -> bool {
    email.contains('@') && email.contains('.') && email.len() > 5
}

/// 验证手机号格式（中国，简化实现）
pub fn is_valid_phone_cn(phone: &str) -> bool {
    phone.len() == 11 && phone.starts_with('1') && phone.chars().all(|c| c.is_numeric())
}

/// 验证URL格式
pub fn is_valid_url(url: &str) -> bool {
    url::Url::parse(url).is_ok()
}

/// 验证IP地址格式
pub fn is_valid_ip(ip: &str) -> bool {
    ip.parse::<std::net::IpAddr>().is_ok()
}

/// 验证端口号
pub fn is_valid_port(port: u16) -> bool {
    port > 0 && port <= 65535
}

/// 验证字符串长度
pub fn is_valid_length(text: &str, min: usize, max: usize) -> bool {
    let len = text.len();
    len >= min && len <= max
}

/// 验证是否只包含字母数字
pub fn is_alphanumeric(text: &str) -> bool {
    text.chars().all(|c| c.is_alphanumeric())
}

/// 验证是否为有效的UUID
pub fn is_valid_uuid(uuid: &str) -> bool {
    uuid::Uuid::parse_str(uuid).is_ok()
}

/// 验证密码强度（至少8位，包含大小写字母和数字）
pub fn is_strong_password(password: &str) -> bool {
    if password.len() < 8 {
        return false;
    }

    let has_lower = password.chars().any(|c| c.is_lowercase());
    let has_upper = password.chars().any(|c| c.is_uppercase());
    let has_digit = password.chars().any(|c| c.is_numeric());

    has_lower && has_upper && has_digit
}

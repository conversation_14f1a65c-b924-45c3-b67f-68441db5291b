use base64::{engine::general_purpose, Engine as _};
use hex;
use sha2::{Digest, Sha256};
use std::time::{SystemTime, UNIX_EPOCH};

/// 生成MD5哈希（简化实现）
pub fn generate_md5(input: &str) -> String {
    // 简化实现，使用SHA256代替MD5
    generate_sha256(input)
}

/// 生成SHA256哈希
pub fn generate_sha256(input: &str) -> String {
    let mut hasher = Sha256::new();
    hasher.update(input.as_bytes());
    let result = hasher.finalize();
    hex::encode(result)
}

/// Base64编码
pub fn base64_encode(input: &[u8]) -> String {
    general_purpose::STANDARD.encode(input)
}

/// Base64解码
pub fn base64_decode(input: &str) -> Result<Vec<u8>, String> {
    general_purpose::STANDARD.decode(input).map_err(|e| format!("Base64解码失败: {}", e))
}

/// 生成当前时间戳（毫秒）
pub fn current_timestamp_millis() -> u64 {
    SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64
}

/// 简单的AES256加密（用于设备ID等）
pub fn simple_aes_encrypt(data: &str, key: &[u8; 32], _iv: &[u8; 16]) -> Result<String, String> {
    // 简化实现，实际项目中应使用真正的AES加密
    let combined = format!("{}:{}", hex::encode(key), data);
    Ok(base64_encode(combined.as_bytes()))
}

/// URL编码（简化实现）
pub fn url_encode(input: &str) -> String {
    // 简化实现，只做基本的URL编码
    input.replace(" ", "%20").replace("&", "%26").replace("=", "%3D")
}

/// 生成随机UUID
pub fn generate_uuid() -> String {
    uuid::Uuid::new_v4().to_string()
}

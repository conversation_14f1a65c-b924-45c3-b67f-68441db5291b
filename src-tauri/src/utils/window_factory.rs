use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use std::collections::HashMap;
use std::sync::Arc;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, WebviewUrl, WebviewWindow, WebviewWindowBuilder};

/// 窗口关闭回调函数类型
pub type WindowCloseCallback = Box<dyn Fn(&WebviewWindow, &tauri::WindowEvent) + Send + Sync>;

/// 窗口创建参数
#[derive(Serialize, Deserialize)]
pub struct WindowParams {
    /// 窗口标题
    pub title: Option<String>,
    /// 窗口配置
    pub window_config: Option<WindowConfig>,
    /// 窗口关闭回调（序列化时忽略）
    #[serde(skip)]
    pub callback: Option<WindowCloseCallback>,
}

impl Default for WindowParams {
    fn default() -> Self {
        Self {
            title: None,
            window_config: None,
            callback: None,
        }
    }
}

/// 窗口配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowConfig {
    /// 窗口宽度
    pub width: Option<f64>,
    /// 窗口高度
    pub height: Option<f64>,
    /// 窗口X坐标
    pub x: Option<f64>,
    /// 窗口Y坐标
    pub y: Option<f64>,
    /// 是否可调整大小
    pub resizable: Option<bool>,
    /// 是否可关闭
    pub closable: Option<bool>,
    /// 是否可最小化
    pub minimizable: Option<bool>,
    /// 是否可最大化
    pub maximizable: Option<bool>,
    /// 是否显示装饰
    pub decorations: Option<bool>,
    /// 是否总是置顶
    pub always_on_top: Option<bool>,
    /// 是否跳过任务栏
    pub skip_taskbar: Option<bool>,
    /// 是否全屏
    pub fullscreen: Option<bool>,
    /// 是否居中
    pub center: Option<bool>,
    /// 最小宽度
    pub min_width: Option<f64>,
    /// 最小高度
    pub min_height: Option<f64>,
    /// 最大宽度
    pub max_width: Option<f64>,
    /// 最大高度
    pub max_height: Option<f64>,
    /// 是否可见
    pub visible: Option<bool>,
    /// 是否聚焦
    pub focused: Option<bool>,
}

impl Default for WindowConfig {
    fn default() -> Self {
        Self {
            width: Some(1200.0),
            height: Some(800.0),
            x: None,
            y: None,
            resizable: Some(true),
            closable: Some(true),
            minimizable: Some(true),
            maximizable: Some(true),
            decorations: Some(true),
            always_on_top: Some(false),
            skip_taskbar: Some(false),
            fullscreen: Some(false),
            center: Some(true),
            min_width: None,
            min_height: None,
            max_width: None,
            max_height: None,
            visible: Some(true),
            focused: Some(true),
        }
    }
}

impl WindowConfig {
    /// 创建小窗口配置
    pub fn small() -> Self {
        Self {
            width: Some(800.0),
            height: Some(600.0),
            ..Default::default()
        }
    }

    /// 创建大窗口配置
    pub fn large() -> Self {
        Self {
            width: Some(1600.0),
            height: Some(1200.0),
            ..Default::default()
        }
    }

    /// 创建全屏配置
    pub fn fullscreen() -> Self {
        Self {
            fullscreen: Some(true),
            ..Default::default()
        }
    }

    /// 创建最小化配置
    pub fn minimized() -> Self {
        Self {
            width: Some(400.0),
            height: Some(300.0),
            ..Default::default()
        }
    }

    /// 设置窗口大小
    pub fn with_size(mut self, width: f64, height: f64) -> Self {
        self.width = Some(width);
        self.height = Some(height);
        self
    }

    /// 设置窗口位置
    pub fn with_position(mut self, x: f64, y: f64) -> Self {
        self.x = Some(x);
        self.y = Some(y);
        self
    }

    /// 设置最小尺寸
    pub fn with_min_size(mut self, min_width: f64, min_height: f64) -> Self {
        self.min_width = Some(min_width);
        self.min_height = Some(min_height);
        self
    }

    /// 设置最大尺寸
    pub fn with_max_size(mut self, max_width: f64, max_height: f64) -> Self {
        self.max_width = Some(max_width);
        self.max_height = Some(max_height);
        self
    }
}

impl WindowParams {
    /// 创建新的窗口参数
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置标题
    pub fn with_title<S: Into<String>>(mut self, title: S) -> Self {
        self.title = Some(title.into());
        self
    }

    /// 设置窗口配置
    pub fn with_config(mut self, config: WindowConfig) -> Self {
        self.window_config = Some(config);
        self
    }

    /// 设置关闭回调
    pub fn with_callback<F>(mut self, callback: F) -> Self
    where
        F: Fn(&WebviewWindow, &tauri::WindowEvent) + Send + Sync + 'static,
    {
        self.callback = Some(Box::new(callback));
        self
    }
}

/// 窗口工厂 - 纯函数式窗口创建接口
pub struct WindowFactory;

impl WindowFactory {
    /// 创建窗口的纯函数接口
    ///
    /// # 参数
    /// - `app_handle`: Tauri 应用句柄
    /// - `url`: 窗口URL（必填）
    /// - `params`: 窗口参数（可选）
    /// - `identity`: 存储隔离标识（可选）
    ///
    /// # 返回
    /// 返回创建的 WebviewWindow 实例
    pub fn create_window(
        app_handle: &AppHandle,
        url: &str,
        params: Option<WindowParams>,
        identity: Option<&str>,
    ) -> Result<WebviewWindow, String> {
        // 生成唯一的窗口标签
        let window_label = format!("window_{}", uuid::Uuid::new_v4());

        Self::create_window_with_label(app_handle, &window_label, url, params, identity)
    }

    /// 使用指定标签创建窗口
    pub fn create_window_with_label(
        app_handle: &AppHandle,
        label: &str,
        url: &str,
        params: Option<WindowParams>,
        identity: Option<&str>,
    ) -> Result<WebviewWindow, String> {
        // 检查窗口是否已存在
        if app_handle.get_webview_window(label).is_some() {
            return Err(format!("窗口 {} 已存在", label));
        }

        let params = params.unwrap_or_default();
        let config = params.window_config.clone().unwrap_or_default();

        // 解析URL
        let webview_url = if url.starts_with("http://") || url.starts_with("https://") {
            WebviewUrl::External(url.parse().map_err(|e| format!("无效的URL: {}", e))?)
        } else {
            WebviewUrl::App(url.into())
        };

        // 创建窗口构建器
        let mut builder = WebviewWindowBuilder::new(app_handle, label, webview_url);

        // 应用窗口配置
        let mut builder = Self::apply_window_config(builder, &config, &params);

        // 设置存储隔离
        if let Some(identity) = identity {
            log::info!("🔐 WindowFactory: 应用存储隔离，identity={}", identity);
            builder = Self::apply_storage_isolation(builder, identity)?;
        } else {
            log::warn!("⚠️ WindowFactory: 未提供identity，将使用默认存储");
        }

        // 创建窗口
        let window = builder
            .build()
            .map_err(|e| format!("创建窗口失败: {}", e))?;

        // 添加事件处理回调（在窗口创建后）
        if let Some(callback) = params.callback {
            let callback = Arc::new(callback);
            let window_clone = window.clone();
            window.on_window_event(move |event| {
                callback(&window_clone, &event);
            });
        }

        Ok(window)
    }

    /// 应用窗口配置到构建器
    fn apply_window_config<'a, R: tauri::Runtime, M: Manager<R>>(
        builder: WebviewWindowBuilder<'a, R, M>,
        config: &WindowConfig,
        params: &WindowParams,
    ) -> WebviewWindowBuilder<'a, R, M> {
        let mut builder = builder;

        // 设置标题
        if let Some(title) = &params.title {
            builder = builder.title(title);
        }

        // 设置尺寸
        if let (Some(width), Some(height)) = (config.width, config.height) {
            builder = builder.inner_size(width, height);
        }

        // 设置位置
        if let (Some(x), Some(y)) = (config.x, config.y) {
            builder = builder.position(x, y);
        }

        // 设置最小尺寸
        if let (Some(min_width), Some(min_height)) = (config.min_width, config.min_height) {
            builder = builder.min_inner_size(min_width, min_height);
        }

        // 设置最大尺寸
        if let (Some(max_width), Some(max_height)) = (config.max_width, config.max_height) {
            builder = builder.max_inner_size(max_width, max_height);
        }

        // 设置布尔属性
        if let Some(resizable) = config.resizable {
            builder = builder.resizable(resizable);
        }
        if let Some(closable) = config.closable {
            builder = builder.closable(closable);
        }
        if let Some(minimizable) = config.minimizable {
            builder = builder.minimizable(minimizable);
        }
        if let Some(maximizable) = config.maximizable {
            builder = builder.maximizable(maximizable);
        }
        if let Some(decorations) = config.decorations {
            builder = builder.decorations(decorations);
        }
        if let Some(always_on_top) = config.always_on_top {
            builder = builder.always_on_top(always_on_top);
        }
        if let Some(skip_taskbar) = config.skip_taskbar {
            builder = builder.skip_taskbar(skip_taskbar);
        }
        if let Some(fullscreen) = config.fullscreen {
            builder = builder.fullscreen(fullscreen);
        }
        if config.center.unwrap_or(false) {
            builder = builder.center();
        }
        if let Some(visible) = config.visible {
            builder = builder.visible(visible);
        }
        if let Some(focused) = config.focused {
            builder = builder.focused(focused);
        }

        builder
    }

    /// 应用存储隔离设置
    fn apply_storage_isolation<'a, R: tauri::Runtime, M: Manager<R>>(
        builder: WebviewWindowBuilder<'a, R, M>,
        identity: &str,
    ) -> Result<WebviewWindowBuilder<'a, R, M>, String> {
        // 根据环境判定使用 data_directory 或者 data_store_identifier
        let builder = if cfg!(target_os = "windows") {
            // Windows 使用 data_directory，放到browser_isolation子文件夹中
            let data_dir = std::path::PathBuf::from("browser_isolation")
                .join(format!("browser_data_{}", identity));
            log::info!(
                "🪟 Windows: 使用data_directory隔离，路径={}",
                data_dir.display()
            );
            builder.data_directory(data_dir)
        } else {
            // 其他平台使用 data_store_identifier
            let identifier = Self::generate_data_store_identifier(identity);
            log::info!(
                "🍎 非Windows: 使用data_store_identifier隔离，标识符={:?}",
                identifier
            );
            builder.data_store_identifier(identifier)
        };

        Ok(builder)
    }

    /// 生成数据存储标识符
    fn generate_data_store_identifier(identity: &str) -> [u8; 16] {
        let mut hasher = Sha256::new();
        hasher.update(identity.as_bytes());
        let hash = hasher.finalize();

        // 取前16字节作为标识符
        let mut identifier = [0u8; 16];
        identifier.copy_from_slice(&hash[0..16]);
        identifier
    }
}

use super::service::{MonitorService, MonitorStatus, MonitorData};
use crate::models::config::MonitorConfig;
use tauri::State;

/// 启动监控
#[tauri::command]
pub async fn monitor_start(
    monitor_service: State<'_, MonitorService>,
    config: MonitorConfig,
) -> Result<(), String> {
    monitor_service.start_monitoring(config).await
}

/// 停止监控
#[tauri::command]
pub async fn monitor_stop(monitor_service: State<'_, MonitorService>) -> Result<(), String> {
    monitor_service.stop_monitoring().await
}

/// 检查监控状态
#[tauri::command]
pub async fn monitor_is_running(monitor_service: State<'_, MonitorService>) -> Result<bool, String> {
    Ok(monitor_service.is_running().await)
}

/// 获取监控状态
#[tauri::command]
pub async fn monitor_get_status(monitor_service: State<'_, MonitorService>) -> Result<MonitorStatus, String> {
    Ok(monitor_service.get_status().await)
}

/// 获取监控数据
#[tauri::command]
pub async fn monitor_get_data(monitor_service: State<'_, MonitorService>) -> Result<MonitorData, String> {
    Ok(monitor_service.get_data().await)
}

/// 清空监控数据
#[tauri::command]
pub async fn monitor_clear_data(monitor_service: State<'_, MonitorService>) -> Result<(), String> {
    monitor_service.clear_data().await
}

/// 更新监控配置
#[tauri::command]
pub async fn monitor_update_config(
    monitor_service: State<'_, MonitorService>,
    config: MonitorConfig,
) -> Result<(), String> {
    monitor_service.update_config(config).await
}

/// 获取当前监控配置
#[tauri::command]
pub async fn monitor_get_config(monitor_service: State<'_, MonitorService>) -> Result<MonitorConfig, String> {
    Ok(monitor_service.get_config().await)
}

/// 暂停指定账号监控
#[tauri::command]
pub async fn monitor_pause_account(
    monitor_service: State<'_, MonitorService>,
    account_id: String,
) -> Result<(), String> {
    monitor_service.pause_account(&account_id).await
}

/// 恢复指定账号监控
#[tauri::command]
pub async fn monitor_resume_account(
    monitor_service: State<'_, MonitorService>,
    account_id: String,
) -> Result<(), String> {
    monitor_service.resume_account(&account_id).await
}

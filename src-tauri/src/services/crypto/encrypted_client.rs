use crate::services::CryptoService;
use reqwest::{cookie::Jar, Client, Proxy};
use std::sync::Arc;
use url::Url;

/// 加密HTTP客户端，统一处理所有需要加密的API请求
#[derive(Clone)]
pub struct EncryptedClient {
    client: Client,
    crypto_service: CryptoService,
    cookie_jar: Arc<Jar>,
}

impl EncryptedClient {
    /// 创建新的加密客户端实例
    pub fn new() -> Result<Self, String> {
        let cookie_jar = Arc::new(Jar::default());
        let crypto_service = CryptoService::new();

        let client = Client::builder()
            .cookie_provider(cookie_jar.clone())
            .build()
            .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

        Ok(Self {
            client,
            crypto_service,
            cookie_jar,
        })
    }

    /// 创建带代理的加密客户端
    pub fn new_with_proxy(proxy_url: &str) -> Result<Self, String> {
        let cookie_jar = Arc::new(Jar::default());
        let crypto_service = CryptoService::new();

        let proxy = Proxy::all(proxy_url).map_err(|e| format!("代理设置失败: {}", e))?;

        let client = Client::builder()
            .cookie_provider(cookie_jar.clone())
            .proxy(proxy)
            .build()
            .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

        Ok(Self {
            client,
            crypto_service,
            cookie_jar,
        })
    }

    /// 设置Cookie到客户端
    pub async fn set_cookies(&self, cookies: &str, base_url: &str) -> Result<(), String> {
        let url = Url::parse(base_url).map_err(|e| format!("URL解析失败: {}", e))?;

        // 验证Cookie格式
        self.crypto_service.validate_cookies(cookies).await?;

        // 设置Cookie到Jar
        let cookies_str = cookies
            .split(";")
            .map(|s| s.trim())
            .collect::<Vec<&str>>()
            .join("; ");

        for cookie in cookies_str.split(";") {
            self.cookie_jar.add_cookie_str(cookie.trim(), &url);
        }

        Ok(())
    }

    /// 发送加密POST请求（带响应头）
    pub async fn post_encrypted_with_headers(
        &self,
        url: &str,
        data: &serde_json::Value,
        cookies: &str,
    ) -> Result<(serde_json::Value, reqwest::header::HeaderMap), String> {
        // 设置Cookie
        self.set_cookies(cookies, url).await?;

        // 解析URL并提取基础信息
        let url_obj = Url::parse(url).map_err(|e| format!("URL解析失败: {}", e))?;
        let full_path = format!(
            "{}{}",
            url_obj.origin().ascii_serialization(),
            url_obj.path()
        );

        // 提取现有查询参数
        let mut query_params: Vec<(String, String)> = url_obj.query_pairs().into_owned().collect();

        // 准备请求数据
        let request_data_string = self.crypto_service.prepare_request_data(data).await?;

        // 生成时间戳
        let timestamp = self.crypto_service.generate_timestamp();

        // 提取签名用的token
        let token = self.crypto_service.extract_token_for_signature(cookies);
        // 生成签名
        let signature = self
            .crypto_service
            .generate_signature(&token, timestamp, &request_data_string)
            .await?;
        // 构建标准查询参数
        let standard_params = self.crypto_service.build_query_params(timestamp, &[]);
        query_params.extend(standard_params);

        // 添加签名
        query_params.push(("sign".to_string(), signature));

        // 构建最终URL
        let mut final_url = Url::parse(&full_path).map_err(|_| "构建最终URL失败".to_string())?;
        for (key, value) in &query_params {
            final_url.query_pairs_mut().append_pair(key, value);
        }

        // 构建表单数据
        let form_body = format!("data={}", request_data_string);

        // 发送请求
        let response = self.client.post(final_url.to_string()).header("Content-Type", "application/x-www-form-urlencoded").body(form_body).send().await.map_err(|e| {
            let error_details = format!("HTTP POST请求失败:\n  URL: {}\n  错误: {}\n  可能原因: 代理服务器不可用(127.0.0.1:9000)或网络连接问题\n  建议: 检查代理服务器是否运行", final_url, e);
            println!("🔥 {}", error_details);
            error_details
        })?;

        // 获取响应头
        let headers = response.headers().clone();

        // 解析响应
        let json_response = response
            .json::<serde_json::Value>()
            .await
            .map_err(|e| format!("响应解析失败: {}", e))?;

        Ok((json_response, headers))
    }

    /// 发送加密POST请求（兼容性方法）
    pub async fn post_encrypted(
        &self,
        url: &str,
        data: &serde_json::Value,
        cookies: &str,
    ) -> Result<serde_json::Value, String> {
        let (json_response, _headers) =
            self.post_encrypted_with_headers(url, data, cookies).await?;
        Ok(json_response)
    }

    /// 获取当前Cookie Jar的引用
    pub fn get_cookie_jar(&self) -> &Arc<Jar> {
        &self.cookie_jar
    }

    /// 获取加密服务的引用
    pub fn get_crypto_service(&self) -> &CryptoService {
        &self.crypto_service
    }
}

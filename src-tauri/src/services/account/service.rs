use crate::models::account::{
    Account, AccountConfig, A<PERSON><PERSON><PERSON><PERSON><PERSON>, AccountStatus, RotationStrategy,
};

use crate::services::crypto::EncryptedClient;
use crate::services::time::TimeService;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use std::sync::Arc;
use tauri::{A<PERSON><PERSON><PERSON><PERSON>, Emitter, Manager};
use tokio::sync::RwLock;
use uuid::Uuid;

/// 账号选择结果
#[derive(Debug, Clone)]
pub struct AccountSelection {
    pub account: Account,
    pub index: usize,
}

/// 账号服务事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountEvent {
    pub event_type: String,
    pub account_id: String,
    pub message: String,
    pub timestamp: String,
}

/// 用户信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UserInfo {
    pub user_id: Option<String>,
    pub nickname: Option<String>,
    pub avatar: Option<String>,
    pub is_valid: bool,
}

/// 登录会话信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginSession {
    pub session_id: String,
    pub data_directory: PathBuf,
    pub description: Option<String>,
    pub created_at: chrono::DateTime<Utc>,
    pub status: LoginSessionStatus,
    pub data_store_identifier: Option<[u8; 16]>, // macOS平台的数据存储标识符
}

/// 登录会话状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LoginSessionStatus {
    Active,    // 登录窗口打开中
    Completed, // 登录完成，等待验证
    Verified,  // Cookie验证成功
    Failed,    // Cookie验证失败
}

/// 账号服务 - 管理多账号轮询和状态
#[derive(Clone)]
pub struct AccountService {
    app_handle: AppHandle,
    config: Arc<RwLock<AccountConfig>>,
    config_file_path: PathBuf,
    current_index: Arc<RwLock<usize>>,
    last_used_times: Arc<RwLock<HashMap<String, chrono::DateTime<Utc>>>>,
    login_sessions: Arc<RwLock<HashMap<String, LoginSession>>>, // 登录会话管理
    data_dir: PathBuf,                                          // 应用数据目录
    time_service: TimeService,                                  // 时间同步服务
}

impl AccountService {
    /// 创建新的账号服务实例
    pub fn new(
        app_handle: AppHandle,
        config_dir: PathBuf,
        app_data_dir: PathBuf,
        time_service: TimeService,
    ) -> Self {
        let config_file_path = config_dir.join("accounts.json");

        // 使用应用数据目录存储会话数据
        let data_dir = app_data_dir;

        println!("📁 账号服务数据目录: {}", data_dir.display());
        println!("📁 账号配置文件: {}", config_file_path.display());

        Self {
            app_handle,
            config: Arc::new(RwLock::new(AccountConfig::default())),
            config_file_path,
            current_index: Arc::new(RwLock::new(0)),
            last_used_times: Arc::new(RwLock::new(HashMap::new())),
            login_sessions: Arc::new(RwLock::new(HashMap::new())),
            data_dir,
            time_service,
        }
    }

    /// 获取app_handle的引用
    pub fn app_handle(&self) -> &AppHandle {
        &self.app_handle
    }

    /// 生成数据存储标识符（统一方法，所有平台使用）
    /// 使用 SHA256 确保跨进程一致性和稳定性
    fn generate_data_store_identifier(session_id: &str) -> [u8; 16] {
        use sha2::{Digest, Sha256};

        // 使用 SHA256 生成稳定的哈希
        let mut hasher = Sha256::new();
        hasher.update(session_id.as_bytes());
        let hash = hasher.finalize();

        // 取前16字节作为标识符
        let mut identifier = [0u8; 16];
        identifier.copy_from_slice(&hash[0..16]);

        println!(
            "🔑 生成稳定数据存储标识符: {:?} (来源: {}, SHA256前16字节)",
            identifier, session_id
        );
        identifier
    }

    /// 初始化账号服务
    pub async fn initialize(&self) -> Result<(), String> {
        // 确保配置目录存在
        if let Some(parent) = self.config_file_path.parent() {
            fs::create_dir_all(parent).map_err(|e| format!("创建账号配置目录失败: {}", e))?;
        }

        // 加载账号配置
        self.load_config().await?;

        println!("✅ 账号服务初始化完成");
        Ok(())
    }

    /// 加载账号配置
    pub async fn load_config(&self) -> Result<AccountConfig, String> {
        let config = if self.config_file_path.exists() {
            match fs::read_to_string(&self.config_file_path) {
                Ok(content) => match serde_json::from_str::<AccountConfig>(&content) {
                    Ok(parsed_config) => match parsed_config.validate() {
                        Ok(_) => parsed_config,
                        Err(e) => {
                            eprintln!("⚠️  账号配置验证失败: {}, 使用默认配置", e);
                            let default_config = AccountConfig::default();
                            self.save_config(&default_config).await?;
                            default_config
                        }
                    },
                    Err(e) => {
                        eprintln!("⚠️  账号配置格式错误: {}, 使用默认配置", e);
                        let default_config = AccountConfig::default();
                        self.save_config(&default_config).await?;
                        default_config
                    }
                },
                Err(e) => {
                    eprintln!("⚠️  读取账号配置失败: {}, 使用默认配置", e);
                    let default_config = AccountConfig::default();
                    self.save_config(&default_config).await?;
                    default_config
                }
            }
        } else {
            // 创建默认账号配置
            let default_config = AccountConfig::default();
            self.save_config(&default_config).await?;
            default_config
        };

        // 更新内存中的配置
        {
            let mut current_config = self.config.write().await;
            *current_config = config.clone();
        }

        // 账号配置加载完成
        Ok(config)
    }

    /// 保存账号配置
    pub async fn save_config(&self, config: &AccountConfig) -> Result<(), String> {
        // 验证配置
        config.validate()?;

        // 序列化配置
        let content = serde_json::to_string_pretty(config)
            .map_err(|e| format!("序列化账号配置失败: {}", e))?;

        // 写入文件
        fs::write(&self.config_file_path, content)
            .map_err(|e| format!("写入账号配置文件失败: {}", e))?;

        // 更新内存中的配置
        {
            let mut current_config = self.config.write().await;
            *current_config = config.clone();
        }

        // 发送配置更新事件
        self.emit_config_updated().await?;

        Ok(())
    }

    /// 获取当前配置
    pub async fn get_config(&self) -> AccountConfig {
        let config = self.config.read().await;
        config.clone()
    }

    /// 添加账号
    pub async fn add_account(
        &self,
        name: String,
        description: Option<String>,
        cookie: Option<String>,
    ) -> Result<Account, String> {
        println!(
            "🚀 AccountService::add_account 开始执行: name={}, description={:?}, has_cookie={}",
            name,
            description,
            cookie.is_some()
        );

        let mut account = Account::new(name);
        if let Some(desc) = description {
            account.description = Some(desc);
        }

        // 如果提供了Cookie，设置到账号中
        if let Some(cookie_value) = cookie {
            if !cookie_value.trim().is_empty() {
                let cookie = AccountCookie::new(cookie_value, "goofish.com".to_string());
                account.set_cookie(cookie);
                println!("🍪 已设置Cookie到新账号");
            }
        }

        println!(
            "📝 创建账号对象: ID={}, name={}, has_cookie={}",
            account.id,
            account.name,
            account.cookie.is_some()
        );

        let updated_config = {
            let mut config = self.config.write().await;
            config.accounts.push(account.clone());
            config.last_updated = Some(Utc::now());
            println!("📊 更新配置，当前账号数量: {}", config.accounts.len());
            config.clone()
        };

        // 保存配置
        println!("💾 开始保存配置...");
        self.save_config(&updated_config).await?;
        println!("✅ 配置保存成功");

        // 发送账号添加事件
        println!("📡 发送账号添加事件...");
        self.emit_account_event(
            "account_added",
            &account.id,
            &format!("账号 {} 已添加", account.name),
        )
        .await?;
        println!("✅ 事件发送成功");

        println!("✅ 账号已添加: {} (ID: {})", account.name, account.id);
        Ok(account)
    }

    /// 使用用户信息添加账号
    pub async fn add_account_with_user_info(
        &self,
        nickname: String,
        user_id: Option<String>,
        cookie: String,
        description: Option<String>,
    ) -> Result<(Account, bool), String> {
        println!(
            "🚀 AccountService::add_account_with_user_info 开始执行: nickname={}, user_id={:?}",
            nickname, user_id
        );

        // 先验证Cookie
        let user_info = self.get_user_info(&cookie).await?;
        if !user_info.is_valid {
            return Err("Cookie验证失败".to_string());
        }

        println!("🔍 Cookie验证成功，开始检查重复账号");

        // 检查是否已存在相同用户ID的账号
        if let Some(uid) = &user_info.user_id {
            println!("🔍 检查用户ID重复: {}", uid);
            println!("🔍 开始获取配置读锁检查重复账号");
            let existing_account = {
                let config = self.config.read().await;
                println!("🔍 已获取配置读锁，开始查找重复账号");
                config
                    .accounts
                    .iter()
                    .find(|account| {
                        if let Some(existing_uid) = account.metadata.get("user_id") {
                            existing_uid == uid
                        } else {
                            false
                        }
                    })
                    .cloned()
            };
            println!("🔍 重复账号检查完成");

            if let Some(existing) = existing_account {
                println!(
                    "🔄 发现重复账号，更新现有账号: {} (ID: {})",
                    existing.name, existing.id
                );

                // 更新现有账号的Cookie和信息
                self.update_account_cookie(&existing.id, cookie).await?;

                // 返回更新后的账号信息
                let updated_account = {
                    let config = self.config.read().await;
                    config
                        .accounts
                        .iter()
                        .find(|a| a.id == existing.id)
                        .cloned()
                        .ok_or_else(|| "更新后无法找到账号".to_string())?
                };

                return Ok((updated_account, true)); // 返回更新标志
            }
        } else {
            println!("🔍 用户信息中没有user_id，跳过重复检查");
        }

        println!("🔍 开始创建新账号");
        let mut account = Account::new(nickname);
        if let Some(desc) = description {
            account.description = Some(desc);
        }

        // 设置Cookie
        let account_cookie = AccountCookie::new(cookie, "goofish.com".to_string());
        account.set_cookie(account_cookie);

        // 保存用户信息到metadata
        if let Some(uid) = user_id {
            account.metadata.insert("user_id".to_string(), uid);
        }
        if let Some(nick) = user_info.nickname {
            // 确保账号名称与昵称保持一致
            account.name = nick.clone();
            account.metadata.insert("nickname".to_string(), nick);
        }
        if let Some(avatar_url) = user_info.avatar {
            account.metadata.insert("avatar".to_string(), avatar_url);
        }

        // 设置为活跃状态
        account.update_status(AccountStatus::Active);

        println!(
            "📝 创建账号对象: ID={}, name={}, has_cookie={}, status={:?}",
            account.id,
            account.name,
            account.cookie.is_some(),
            account.status
        );

        let updated_config = {
            let mut config = self.config.write().await;
            config.accounts.push(account.clone());
            config.last_updated = Some(Utc::now());
            println!("📊 更新配置，当前账号数量: {}", config.accounts.len());
            config.clone()
        };

        // 保存配置
        println!("💾 开始保存配置...");
        self.save_config(&updated_config).await?;
        println!("✅ 配置保存成功");

        // 发送账号添加事件
        println!("📡 发送账号添加事件...");
        self.emit_account_event(
            "account_added",
            &account.id,
            &format!("账号 {} 已添加", account.name),
        )
        .await?;
        println!("✅ 事件发送成功");

        println!("✅ 账号已添加: {} (ID: {})", account.name, account.id);
        Ok((account, false)) // 返回新添加标志
    }

    /// 保存会话ID到账号metadata
    pub async fn save_session_id_to_account(
        &self,
        account_id: &str,
        session_id: &str,
    ) -> Result<(), String> {
        log::info!(
            "💾 保存会话ID到账号: account_id={}, session_id={}",
            account_id,
            session_id
        );

        let updated_config = {
            let mut config = self.config.write().await;
            log::info!("🔍 当前配置中的账号数量: {}", config.accounts.len());
            if let Some(account) = config.accounts.iter_mut().find(|a| a.id == account_id) {
                log::info!("🔍 找到账号: {} (ID: {})", account.name, account.id);
                log::info!("🔍 保存前的metadata: {:?}", account.metadata);
                account
                    .metadata
                    .insert("login_session_id".to_string(), session_id.to_string());
                log::info!("🔍 保存后的metadata: {:?}", account.metadata);
                log::info!("✅ 已保存会话ID到账号metadata");
            } else {
                log::error!("❌ 未找到账号: {}", account_id);
                log::info!("🔍 现有账号列表:");
                for acc in &config.accounts {
                    log::info!("  - {} (ID: {})", acc.name, acc.id);
                }
                return Err(format!("账号不存在: {}", account_id));
            }
            config.clone()
        };

        // 保存配置
        self.save_config(&updated_config).await?;
        log::info!("✅ 配置已保存");
        Ok(())
    }

    /// 创建登录会话
    pub async fn create_login_session(
        &self,
        description: Option<String>,
    ) -> Result<String, String> {
        let session_id = Uuid::new_v4().to_string();
        let session_dir = self.data_dir.join("login_sessions").join(&session_id);

        // 创建会话目录
        if let Err(e) = fs::create_dir_all(&session_dir) {
            return Err(format!("创建会话目录失败: {}", e));
        }

        // 确保使用绝对路径
        let absolute_session_dir = session_dir
            .canonicalize()
            .unwrap_or_else(|_| session_dir.clone());

        // 在 macOS 上设置目录权限，确保应用重启后仍可访问
        #[cfg(target_os = "macos")]
        {
            use std::os::unix::fs::PermissionsExt;
            if let Ok(metadata) = fs::metadata(&absolute_session_dir) {
                let mut perms = metadata.permissions();
                perms.set_mode(0o755); // 设置为 rwxr-xr-x
                let mode = perms.mode(); // 先获取 mode 值
                let _ = fs::set_permissions(&absolute_session_dir, perms);
                println!("🔐 已设置会话目录权限: {:o}", mode);
            }
        }

        // 统一生成数据存储标识符（所有平台都生成）
        let data_store_identifier = Self::generate_data_store_identifier(&session_id);

        let session = LoginSession {
            session_id: session_id.clone(),
            data_directory: absolute_session_dir,
            description,
            created_at: Utc::now(),
            status: LoginSessionStatus::Active,
            data_store_identifier: Some(data_store_identifier),
        };

        // 保存会话信息
        {
            let mut sessions = self.login_sessions.write().await;
            sessions.insert(session_id.clone(), session);
        }

        println!("🆔 创建登录会话: {}", session_id);
        Ok(session_id)
    }

    /// 获取登录会话
    pub async fn get_login_session(&self, session_id: &str) -> Option<LoginSession> {
        let sessions = self.login_sessions.read().await;
        sessions.get(session_id).cloned()
    }

    /// 更新登录会话的数据存储标识符（macOS专用）
    pub async fn update_session_data_store_identifier(
        &self,
        session_id: &str,
        identifier: [u8; 16],
    ) -> Result<(), String> {
        let mut sessions = self.login_sessions.write().await;
        if let Some(session) = sessions.get_mut(session_id) {
            session.data_store_identifier = Some(identifier);
            println!(
                "💾 已更新会话 {} 的数据存储标识符: {:?}",
                session_id, identifier
            );
            Ok(())
        } else {
            Err(format!("会话不存在: {}", session_id))
        }
    }

    /// 更新登录会话状态
    pub async fn update_login_session_status(
        &self,
        session_id: &str,
        status: LoginSessionStatus,
    ) -> Result<(), String> {
        let mut sessions = self.login_sessions.write().await;
        if let Some(session) = sessions.get_mut(session_id) {
            session.status = status;
            println!(
                "🔄 更新登录会话状态: {} -> {:?}",
                session_id, session.status
            );
            Ok(())
        } else {
            Err(format!("登录会话不存在: {}", session_id))
        }
    }

    /// 清理登录会话
    pub async fn cleanup_login_session(&self, session_id: &str) -> Result<(), String> {
        let session = {
            let mut sessions = self.login_sessions.write().await;
            sessions.remove(session_id)
        };

        if let Some(session) = session {
            // 统一的清理逻辑（所有平台）
            if let Some(identifier) = session.data_store_identifier {
                println!("🗑️ 会话使用标识符 {:?}，WebKit将自动管理数据", identifier);
            }

            // 删除我们创建的目录（如果存在）
            if session.data_directory.exists() {
                if let Err(e) = fs::remove_dir_all(&session.data_directory) {
                    println!("⚠️ 删除会话目录失败: {}", e);
                } else {
                    println!("🗑️ 已删除会话目录: {:?}", session.data_directory);
                }
            }

            println!("🧹 清理登录会话: {}", session_id);
        }

        Ok(())
    }

    /// 从登录会话目录提取Cookie
    pub async fn extract_cookies_from_session(&self, session_id: &str) -> Result<String, String> {
        let session = self
            .get_login_session(session_id)
            .await
            .ok_or_else(|| format!("登录会话不存在: {}", session_id))?;

        // 查找Cookie文件（通常在用户数据目录的Cookies文件中）
        let cookies_file = session.data_directory.join("Default").join("Cookies");

        if !cookies_file.exists() {
            return Err("Cookie文件不存在".to_string());
        }

        // 这里需要实现从SQLite数据库中读取Cookie的逻辑
        // 由于Cookie存储在SQLite数据库中，我们需要解析它
        // 暂时返回错误，提示需要实现Cookie提取逻辑
        Err("Cookie提取功能待实现".to_string())
    }

    /// 处理登录会话完成
    pub async fn handle_login_session_completed(&self, session_id: &str) -> Result<(), String> {
        println!("🔄 处理登录会话完成: {}", session_id);

        // 更新会话状态
        self.update_login_session_status(session_id, LoginSessionStatus::Completed)
            .await?;

        // 尝试提取Cookie
        match self.extract_cookies_from_session(session_id).await {
            Ok(cookie) => {
                println!("🍪 成功提取Cookie，开始验证");

                // 验证Cookie并获取用户信息
                match self.get_user_info(&cookie).await {
                    Ok(user_info) if user_info.is_valid => {
                        println!("✅ Cookie验证成功，添加账号");

                        // 获取会话描述
                        let description = self
                            .get_login_session(session_id)
                            .await
                            .and_then(|s| s.description);

                        // 添加账号
                        match self
                            .add_account_with_user_info(
                                user_info
                                    .nickname
                                    .clone()
                                    .unwrap_or_else(|| "未知用户".to_string()),
                                user_info.user_id.clone(),
                                cookie,
                                description,
                            )
                            .await
                        {
                            Ok((account, is_update)) => {
                                // 保存会话ID到账号metadata
                                let updated_config = {
                                    let mut config = self.config.write().await;
                                    if let Some(acc) =
                                        config.accounts.iter_mut().find(|a| a.id == account.id)
                                    {
                                        acc.metadata.insert(
                                            "login_session_id".to_string(),
                                            session_id.to_string(),
                                        );
                                    }
                                    config.clone()
                                };

                                // 在释放锁后保存配置
                                self.save_config(&updated_config).await?;

                                self.update_login_session_status(
                                    session_id,
                                    LoginSessionStatus::Verified,
                                )
                                .await?;

                                let message = if is_update {
                                    format!(
                                        "账号 {} 已更新",
                                        user_info
                                            .nickname
                                            .unwrap_or_else(|| "未知用户".to_string())
                                    )
                                } else {
                                    format!(
                                        "账号 {} 已添加",
                                        user_info
                                            .nickname
                                            .unwrap_or_else(|| "未知用户".to_string())
                                    )
                                };

                                // 发送成功事件
                                self.emit_account_event(
                                    "login_session_success",
                                    &account.id,
                                    &message,
                                )
                                .await?;

                                println!("✅ 登录会话处理成功: {}", message);
                            }
                            Err(e) => {
                                println!("❌ 添加账号失败: {}", e);
                                self.update_login_session_status(
                                    session_id,
                                    LoginSessionStatus::Failed,
                                )
                                .await?;
                                self.cleanup_login_session(session_id).await?;
                            }
                        }
                    }
                    Ok(_) => {
                        println!("❌ Cookie验证失败");
                        self.update_login_session_status(session_id, LoginSessionStatus::Failed)
                            .await?;
                        self.cleanup_login_session(session_id).await?;
                    }
                    Err(e) => {
                        println!("❌ Cookie验证出错: {}", e);
                        self.update_login_session_status(session_id, LoginSessionStatus::Failed)
                            .await?;
                        self.cleanup_login_session(session_id).await?;
                    }
                }
            }
            Err(e) => {
                println!("❌ Cookie提取失败: {}", e);
                self.update_login_session_status(session_id, LoginSessionStatus::Failed)
                    .await?;
                self.cleanup_login_session(session_id).await?;
            }
        }

        Ok(())
    }

    /// 处理登录会话完成（带Cookie）
    pub async fn handle_login_session_completed_with_cookie(
        &self,
        session_id: &str,
        cookie_string: String,
    ) -> Result<(), String> {
        println!("🔄 处理登录会话完成（带Cookie）: {}", session_id);

        // 首先检查会话是否存在
        if self.get_login_session(session_id).await.is_none() {
            println!("⚠️ 登录会话不存在，跳过处理: {}", session_id);
            return Ok(());
        }

        // 更新会话状态
        if let Err(e) = self
            .update_login_session_status(session_id, LoginSessionStatus::Completed)
            .await
        {
            println!("⚠️ 更新会话状态失败: {}，继续处理Cookie", e);
            // 即使状态更新失败，也继续处理Cookie，因为Cookie数据更重要
        }

        // 验证Cookie并获取用户信息
        match self.get_user_info(&cookie_string).await {
            Ok(user_info) if user_info.is_valid => {
                println!("✅ Cookie验证成功，添加账号");

                // 获取会话描述
                let description = self
                    .get_login_session(session_id)
                    .await
                    .and_then(|s| s.description);

                // 添加账号
                match self
                    .add_account_with_user_info(
                        user_info
                            .nickname
                            .clone()
                            .unwrap_or_else(|| "未知用户".to_string()),
                        user_info.user_id.clone(),
                        cookie_string,
                        description,
                    )
                    .await
                {
                    Ok((account, is_update)) => {
                        // 保存会话ID和平台特定信息到账号metadata
                        let updated_config = {
                            let mut config = self.config.write().await;
                            if let Some(acc) =
                                config.accounts.iter_mut().find(|a| a.id == account.id)
                            {
                                acc.metadata
                                    .insert("login_session_id".to_string(), session_id.to_string());

                                // 获取会话信息并保存标识符（所有平台统一）
                                if let Some(session) = self.get_login_session(session_id).await {
                                    if let Some(identifier) = session.data_store_identifier {
                                        acc.metadata.insert(
                                            "data_store_identifier".to_string(),
                                            base64::encode(identifier),
                                        );
                                        println!(
                                            "💾 保存数据存储标识符到账号metadata: {:?}",
                                            identifier
                                        );
                                    }

                                    // 同时保存数据目录路径（用于清理）
                                    acc.metadata.insert(
                                        "data_directory".to_string(),
                                        session.data_directory.to_string_lossy().to_string(),
                                    );
                                    println!("💾 保存数据目录路径到账号metadata");
                                }
                            }
                            config.clone()
                        };

                        // 在释放锁后保存配置
                        self.save_config(&updated_config).await?;

                        self.update_login_session_status(session_id, LoginSessionStatus::Verified)
                            .await?;

                        let message = if is_update {
                            format!(
                                "账号 {} 已更新",
                                user_info.nickname.unwrap_or_else(|| "未知用户".to_string())
                            )
                        } else {
                            format!(
                                "账号 {} 已添加",
                                user_info.nickname.unwrap_or_else(|| "未知用户".to_string())
                            )
                        };

                        // 发送成功事件
                        self.emit_account_event("login_session_success", &account.id, &message)
                            .await?;

                        println!("✅ 登录会话处理成功: {}", message);
                    }
                    Err(e) => {
                        println!("❌ 添加账号失败: {}", e);
                        self.update_login_session_status(session_id, LoginSessionStatus::Failed)
                            .await?;
                        self.cleanup_login_session(session_id).await?;
                    }
                }
            }
            Ok(_) => {
                println!("❌ Cookie验证失败");
                self.update_login_session_status(session_id, LoginSessionStatus::Failed)
                    .await?;
                self.cleanup_login_session(session_id).await?;
            }
            Err(e) => {
                println!("❌ Cookie验证出错: {}", e);
                self.update_login_session_status(session_id, LoginSessionStatus::Failed)
                    .await?;
                self.cleanup_login_session(session_id).await?;
            }
        }

        Ok(())
    }

    /// 重用现有会话ID更新账号Cookie
    pub async fn reuse_login_session_for_account(
        &self,
        account_id: &str,
    ) -> Result<String, String> {
        println!("🔄 重用登录会话更新账号Cookie: {}", account_id);

        // 获取账号信息
        let account = {
            let config = self.config.read().await;
            config
                .accounts
                .iter()
                .find(|a| a.id == account_id)
                .cloned()
                .ok_or_else(|| format!("账号不存在: {}", account_id))?
        };

        // 检查是否有保存的会话ID
        log::info!("🔍 检查账号metadata: {:?}", account.metadata);
        if let Some(session_id) = account.metadata.get("login_session_id") {
            log::info!(
                "🔍 找到保存的会话ID: {} (账号: {})",
                session_id,
                account.name
            );

            // 检查会话目录是否还存在，如果不存在则重新创建
            let session_dir = self.data_dir.join("login_sessions").join(session_id);

            // 确保会话目录存在
            if let Err(e) = fs::create_dir_all(&session_dir) {
                println!("⚠️ 重新创建会话目录失败: {}", e);
            } else {
                println!("✅ 会话目录已确保存在，重用会话ID: {}", session_id);
            }

            // 确保使用绝对路径
            let absolute_session_dir = session_dir
                .canonicalize()
                .unwrap_or_else(|_| session_dir.clone());

            // 在 macOS 上确保目录权限正确
            #[cfg(target_os = "macos")]
            {
                use std::os::unix::fs::PermissionsExt;
                if let Ok(metadata) = fs::metadata(&absolute_session_dir) {
                    let mut perms = metadata.permissions();
                    perms.set_mode(0o755); // 设置为 rwxr-xr-x
                    let mode = perms.mode(); // 先获取 mode 值
                    let _ = fs::set_permissions(&absolute_session_dir, perms);
                    println!("🔐 已重新设置会话目录权限: {:o}", mode);
                }
            }

            // 重新生成数据存储标识符（使用稳定的 SHA256 算法）
            let data_store_identifier = Self::generate_data_store_identifier(session_id);

            // 重新激活会话
            let session = LoginSession {
                session_id: session_id.clone(),
                data_directory: absolute_session_dir,
                description: Some(format!("更新账号 {} 的Cookie", account.name)),
                created_at: chrono::Utc::now(),
                status: LoginSessionStatus::Active,
                data_store_identifier: Some(data_store_identifier),
            };

            // 保存会话信息
            {
                let mut sessions = self.login_sessions.write().await;
                sessions.insert(session_id.clone(), session);
            }

            // 会话已重用，返回会话ID（不在这里打开窗口，避免递归）
            println!("✅ 重用现有会话: {}", session_id);

            return Ok(session_id.clone());
        } else {
            log::warn!(
                "⚠️ 账号没有保存的会话ID，创建新会话 (账号: {}, ID: {})",
                account.name,
                account.id
            );
        }

        // 如果没有现有会话或会话目录不存在，创建新会话
        let new_session_id = self
            .create_login_session(Some(format!("更新账号 {} 的Cookie", account.name)))
            .await?;

        // 更新账号的会话ID
        let updated_config = {
            let mut config = self.config.write().await;
            if let Some(acc) = config.accounts.iter_mut().find(|a| a.id == account_id) {
                acc.metadata
                    .insert("login_session_id".to_string(), new_session_id.clone());
            }
            config.clone()
        };

        // 在释放锁后保存配置
        self.save_config(&updated_config).await?;

        println!("✅ 已为账号创建新的登录会话: {}", new_session_id);
        Ok(new_session_id)
    }

    /// 使用会话更新账号Cookie
    pub async fn update_account_cookie_with_session(
        &self,
        account_id: &str,
        session_id: &str,
        cookie_string: String,
    ) -> Result<(), String> {
        println!(
            "🔄 使用会话更新账号Cookie: {} (会话ID: {})",
            account_id, session_id
        );

        // 首先检查会话是否存在
        if self.get_login_session(session_id).await.is_none() {
            println!("⚠️ 登录会话不存在，跳过处理: {}", session_id);
            return Ok(());
        }

        // 更新会话状态
        if let Err(e) = self
            .update_login_session_status(session_id, LoginSessionStatus::Completed)
            .await
        {
            println!("⚠️ 更新会话状态失败: {}，继续处理Cookie", e);
            // 即使状态更新失败，也继续处理Cookie，因为Cookie数据更重要
        }

        // 验证Cookie并获取用户信息
        match self.get_user_info(&cookie_string).await {
            Ok(user_info) if user_info.is_valid => {
                println!("✅ Cookie验证成功，更新账号");

                // 更新账号Cookie和用户信息
                let updated_config = {
                    let mut config = self.config.write().await;
                    if let Some(account) = config.accounts.iter_mut().find(|a| a.id == account_id) {
                        // 更新Cookie
                        account.cookie = Some(AccountCookie {
                            value: cookie_string,
                            domain: "goofish.com".to_string(),
                            path: "/".to_string(),
                            expires: None,
                            secure: true,
                            http_only: false,
                            created_at: chrono::Utc::now(),
                            last_validated_at: Some(chrono::Utc::now()),
                        });

                        // 更新用户信息
                        if let Some(nickname) = user_info.nickname {
                            // 同步更新账号名称与昵称保持一致
                            account.name = nickname.clone();
                            account
                                .metadata
                                .insert("nickname".to_string(), nickname.clone());
                        }
                        if let Some(avatar) = user_info.avatar {
                            account.metadata.insert("avatar".to_string(), avatar);
                        }
                        if let Some(user_id) = user_info.user_id {
                            account.metadata.insert("user_id".to_string(), user_id);
                        }

                        // 更新时间戳
                        account.updated_at = chrono::Utc::now();
                        account.status = AccountStatus::Active;

                        println!("✅ 账号 {} Cookie已更新", account.name);
                        config.clone()
                    } else {
                        return Err(format!("账号不存在: {}", account_id));
                    }
                };

                // 在释放锁后保存配置
                self.save_config(&updated_config).await?;

                self.update_login_session_status(session_id, LoginSessionStatus::Verified)
                    .await?;

                let message = format!("账号Cookie已成功更新");

                // 发送成功事件
                self.emit_account_event("account_cookie_updated", account_id, &message)
                    .await?;

                // 发送监控恢复事件
                self.emit_account_cookie_updated_for_monitor(account_id)
                    .await?;

                println!("✅ 账号Cookie更新成功: {}", message);
            }
            Ok(_) => {
                println!("❌ Cookie验证失败");
                self.update_login_session_status(session_id, LoginSessionStatus::Failed)
                    .await?;
                self.cleanup_login_session(session_id).await?;
                return Err("Cookie验证失败".to_string());
            }
            Err(e) => {
                println!("❌ Cookie验证出错: {}", e);
                self.update_login_session_status(session_id, LoginSessionStatus::Failed)
                    .await?;
                self.cleanup_login_session(session_id).await?;
                return Err(format!("Cookie验证出错: {}", e));
            }
        }

        Ok(())
    }

    /// 删除账号
    pub async fn remove_account(&self, account_id: &str) -> Result<(), String> {
        let updated_config = {
            let mut config = self.config.write().await;

            let initial_len = config.accounts.len();
            config.accounts.retain(|a| a.id != account_id);

            if config.accounts.len() == initial_len {
                return Err(format!("账号不存在: {}", account_id));
            }

            config.last_updated = Some(Utc::now());
            config.clone()
        };

        // 保存配置
        self.save_config(&updated_config).await?;

        // 发送账号删除事件
        self.emit_account_event("account_removed", account_id, "账号已删除")
            .await?;

        println!("🗑️ 账号已删除: {}", account_id);
        Ok(())
    }

    /// 更新账号Cookie
    pub async fn update_account_cookie(
        &self,
        account_id: &str,
        cookie_value: String,
    ) -> Result<(), String> {
        println!(
            "🍪 开始更新账号Cookie: account_id={}, cookie_length={}",
            account_id,
            cookie_value.len()
        );

        // 首先验证Cookie并获取用户信息
        let user_info = self.get_user_info(&cookie_value).await?;

        if !user_info.is_valid {
            return Err("Cookie验证失败，请检查Cookie是否正确".to_string());
        }

        let (account_name, updated_config) = {
            let mut config = self.config.write().await;

            if let Some(account) = config.accounts.iter_mut().find(|a| a.id == account_id) {
                println!("🍪 找到账号: {}", account.name);

                // 使用默认域名 goofish.com
                let cookie = AccountCookie::new(cookie_value.clone(), "goofish.com".to_string());
                println!(
                    "🍪 创建Cookie对象: value_length={}, domain={}",
                    cookie.value.len(),
                    cookie.domain
                );
                account.set_cookie(cookie);

                // 保存用户信息到metadata
                if let Some(uid) = user_info.user_id {
                    account.metadata.insert("user_id".to_string(), uid);
                    println!("💾 已保存用户ID");
                }
                if let Some(nick) = user_info.nickname {
                    // 同步更新账号名称与昵称保持一致
                    account.name = nick.clone();
                    account
                        .metadata
                        .insert("nickname".to_string(), nick.clone());
                    println!("💾 已保存用户昵称并同步更新账号名称: {}", nick);
                }
                if let Some(avatar_url) = user_info.avatar {
                    account
                        .metadata
                        .insert("avatar".to_string(), avatar_url.clone());
                    println!("💾 已保存用户头像: {}", avatar_url);
                }

                // 设置为活跃状态
                account.update_status(AccountStatus::Active);

                println!("🍪 Cookie已设置到账号并验证成功");
                let name = account.name.clone();
                config.last_updated = Some(Utc::now());
                (name, config.clone())
            } else {
                return Err(format!("账号不存在: {}", account_id));
            }
        };

        // 保存配置
        self.save_config(&updated_config).await?;

        // 发送Cookie更新事件
        self.emit_account_event(
            "cookie_updated",
            account_id,
            &format!("账号 {} Cookie已更新", account_name),
        )
        .await?;

        println!("🍪 账号Cookie已更新: {}", account_name);
        Ok(())
    }

    /// 验证所有账号Cookie有效性
    pub async fn validate_all_accounts(&self) -> Result<(), String> {
        let config = self.config.read().await;
        let enabled_accounts: Vec<_> = config.accounts.iter().filter(|a| a.enabled).collect();

        if enabled_accounts.is_empty() {
            return Err("没有启用的账号，请先添加并启用账号".to_string());
        }

        // 开始验证启用账号的Cookie有效性

        let mut invalid_accounts = Vec::new();

        for account in enabled_accounts {
            if let Some(cookie) = &account.cookie {
                // 验证账号Cookie

                match self.get_user_info(&cookie.value).await {
                    Ok(user_info) if user_info.is_valid => {
                        // 账号Cookie有效
                    }
                    Ok(_) => {
                        // 账号Cookie无效
                        invalid_accounts.push(account.name.clone());
                    }
                    Err(e) => {
                        // 账号Cookie验证失败
                        invalid_accounts.push(account.name.clone());
                    }
                }
            } else {
                // 账号没有Cookie
                invalid_accounts.push(account.name.clone());
            }
        }

        if !invalid_accounts.is_empty() {
            return Err(format!(
                "以下账号Cookie无效，请重新验证: {}",
                invalid_accounts.join(", ")
            ));
        }

        // 所有启用账号Cookie验证通过
        Ok(())
    }

    /// 获取下一个可用账号（轮询逻辑）
    pub async fn get_next_available_account(&self) -> Result<AccountSelection, String> {
        let config = self.config.read().await;
        let mut available_accounts = Vec::new();
        for (index, account) in config.accounts.iter().enumerate() {
            if self.is_account_usable(account).await {
                available_accounts.push((index, account));
            }
        }

        if available_accounts.is_empty() {
            return Err("没有可用的账号，请添加账号或检查账号状态".to_string());
        }

        let selection = match config.rotation_config.strategy {
            RotationStrategy::RoundRobin => self.select_round_robin(&available_accounts).await,
            RotationStrategy::Priority => self.select_by_priority(&available_accounts).await,
            RotationStrategy::Random => self.select_random(&available_accounts).await,
        };

        println!(
            "🔄 选择账号: {} (策略: {:?})",
            selection.account.name, config.rotation_config.strategy
        );
        Ok(selection)
    }

    /// 检查账号是否可用（简化版本）
    async fn is_account_usable(&self, account: &Account) -> bool {
        // 只检查基本可用性：启用状态、有Cookie、状态为Active
        account.enabled
            && account.cookie.is_some()
            && matches!(account.status, AccountStatus::Active)
    }

    /// 轮询选择策略
    async fn select_round_robin(
        &self,
        available_accounts: &[(usize, &Account)],
    ) -> AccountSelection {
        let mut current_index = self.current_index.write().await;

        // 找到当前索引之后的第一个可用账号
        let start_index = *current_index;
        for i in 0..available_accounts.len() {
            let check_index = (start_index + i) % available_accounts.len();
            if let Some((original_index, account)) = available_accounts.get(check_index) {
                *current_index = (check_index + 1) % available_accounts.len();
                return AccountSelection {
                    account: (*account).clone(),
                    index: *original_index,
                };
            }
        }

        // 如果没找到，返回第一个
        *current_index = 1 % available_accounts.len();
        AccountSelection {
            account: available_accounts[0].1.clone(),
            index: available_accounts[0].0,
        }
    }

    /// 优先级选择策略
    async fn select_by_priority(
        &self,
        available_accounts: &[(usize, &Account)],
    ) -> AccountSelection {
        let best = available_accounts
            .iter()
            .min_by_key(|(_, account)| account.priority)
            .unwrap();

        AccountSelection {
            account: best.1.clone(),
            index: best.0,
        }
    }

    /// 随机选择策略
    async fn select_random(&self, available_accounts: &[(usize, &Account)]) -> AccountSelection {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        let index = rng.gen_range(0..available_accounts.len());
        let selected = &available_accounts[index];

        AccountSelection {
            account: selected.1.clone(),
            index: selected.0,
        }
    }

    /// 记录账号调用结果
    pub async fn record_account_usage(
        &self,
        account_id: &str,
        success: bool,
        error_message: Option<String>,
    ) -> Result<(), String> {
        let (account_name, error_msg, updated_config) = {
            let mut config = self.config.write().await;

            if let Some(account) = config.accounts.iter_mut().find(|a| a.id == account_id) {
                account.record_call(success);

                let error_msg = if !success {
                    if let Some(error) = error_message {
                        account.update_status(AccountStatus::Error(error.clone()));
                        Some(error)
                    } else {
                        None
                    }
                } else {
                    None
                };

                let name = account.name.clone();
                config.last_updated = Some(Utc::now());
                (name, error_msg, config.clone())
            } else {
                return Err(format!("账号不存在: {}", account_id));
            }
        };

        // 发送错误事件
        if let Some(error) = &error_msg {
            self.emit_account_event("account_error", account_id, error)
                .await?;
        }

        // 保存配置（使用克隆的配置，避免死锁）
        self.save_config(&updated_config).await?;

        // 账号使用记录已更新
        Ok(())
    }

    /// 发送配置更新事件
    async fn emit_config_updated(&self) -> Result<(), String> {
        self.app_handle
            .emit("account_config_updated", ())
            .map_err(|e| format!("发送账号配置更新事件失败: {}", e))
    }

    /// 发送账号Cookie更新完成事件（用于监控服务恢复）
    async fn emit_account_cookie_updated_for_monitor(
        &self,
        account_id: &str,
    ) -> Result<(), String> {
        self.app_handle
            .emit("account_cookie_updated_for_monitor", account_id)
            .map_err(|e| format!("发送账号Cookie更新事件失败: {}", e))
    }

    /// 发送账号事件
    async fn emit_account_event(
        &self,
        event_type: &str,
        account_id: &str,
        message: &str,
    ) -> Result<(), String> {
        let event = AccountEvent {
            event_type: event_type.to_string(),
            account_id: account_id.to_string(),
            message: message.to_string(),
            timestamp: Utc::now().to_rfc3339(),
        };

        self.app_handle
            .emit("account_event", &event)
            .map_err(|e| format!("发送账号事件失败: {}", e))
    }

    /// 获取所有账号列表
    pub async fn get_all_accounts(&self) -> Vec<Account> {
        let config = self.config.read().await;
        config.accounts.clone()
    }

    /// 获取指定账号
    pub async fn get_account(&self, account_id: &str) -> Option<Account> {
        let config = self.config.read().await;
        config.accounts.iter().find(|a| a.id == account_id).cloned()
    }

    /// 更新账号信息
    pub async fn update_account(
        &self,
        account_id: &str,
        name: Option<String>,
        description: Option<String>,
        enabled: Option<bool>,
        priority: Option<u32>,
    ) -> Result<(), String> {
        let (account_name, updated_config) = {
            let mut config = self.config.write().await;

            if let Some(account) = config.accounts.iter_mut().find(|a| a.id == account_id) {
                if let Some(new_name) = name {
                    account.name = new_name;
                }
                if let Some(new_desc) = description {
                    account.description = Some(new_desc);
                }
                if let Some(new_enabled) = enabled {
                    account.set_enabled(new_enabled);
                }
                if let Some(new_priority) = priority {
                    account.set_priority(new_priority);
                }

                let name = account.name.clone();
                config.last_updated = Some(Utc::now());
                (name, config.clone())
            } else {
                return Err(format!("账号不存在: {}", account_id));
            }
        };

        // 保存配置
        self.save_config(&updated_config).await?;

        // 发送账号更新事件
        self.emit_account_event(
            "account_updated",
            account_id,
            &format!("账号 {} 信息已更新", account_name),
        )
        .await?;

        println!("✏️ 账号信息已更新: {}", account_name);
        Ok(())
    }

    /// 获取用户信息（验证Cookie并获取用户ID和昵称）
    pub async fn get_user_info(&self, cookie: &str) -> Result<UserInfo, String> {
        println!("🔍 开始获取用户信息");

        // 创建加密客户端
        let client = EncryptedClient::new().map_err(|e| format!("创建加密客户端失败: {}", e))?;

        // 构建请求URL和数据
        let url = "https://h5api.m.goofish.com/h5/mtop.idle.web.user.page.head/1.0/";
        let request_data = serde_json::json!({"self": true});

        println!("🌐 发送用户信息请求");

        // 使用加密客户端发送请求（获取响应头以提取服务器时间）
        match client
            .post_encrypted_with_headers(url, &request_data, cookie)
            .await
        {
            Ok((response, headers)) => {
                println!("🌐 响应头: {:?}", headers);
                // 提取服务器时间并同步
                if let Some(date_header) = headers.get("date") {
                    if let Ok(date_str) = date_header.to_str() {
                        println!("🕒 检测到服务器时间: {}", date_str);
                        if let Err(e) = self
                            .time_service
                            .update_from_response_header(date_str)
                            .await
                        {
                            println!("⚠️ 服务器时间同步失败: {}", e);
                        }
                    }
                }

                // 解析响应获取用户信息
                if let Some(data) = response.get("data") {
                    // 检查是否有baseInfo（表示请求成功）
                    if let Some(base_info) = data.get("baseInfo") {
                        // 获取用户ID
                        let user_id = base_info
                            .get("kcUserId")
                            .and_then(|v| v.as_str())
                            .map(|s| s.to_string());

                        // 获取昵称和头像
                        let mut nickname = None;
                        let mut avatar = None;

                        if let Some(module) = data.get("module") {
                            if let Some(base) = module.get("base") {
                                // 获取昵称
                                nickname = base
                                    .get("displayName")
                                    .and_then(|v| v.as_str())
                                    .map(|s| s.to_string());

                                // 获取头像
                                if let Some(avatar_obj) = base.get("avatar") {
                                    avatar = avatar_obj
                                        .get("avatar")
                                        .and_then(|v| v.as_str())
                                        .map(|s| s.to_string());
                                }
                            }
                        }

                        println!(
                            "✅ 获取用户信息成功: ID={:?}, 昵称={:?}, 头像={:?}",
                            user_id, nickname, avatar
                        );

                        return Ok(UserInfo {
                            user_id,
                            nickname,
                            avatar,
                            is_valid: true,
                        });
                    }
                }

                println!("❌ Cookie验证失败或响应格式异常");
                Ok(UserInfo {
                    user_id: None,
                    nickname: None,
                    avatar: None,
                    is_valid: false,
                })
            }
            Err(e) => {
                println!("❌ 请求失败: {}", e);
                Ok(UserInfo {
                    user_id: None,
                    nickname: None,
                    avatar: None,
                    is_valid: false,
                })
            }
        }
    }

    /// 验证账号Cookie有效性
    pub async fn validate_account_cookie(&self, account_id: &str) -> Result<bool, String> {
        println!("🔍 开始验证账号Cookie: {}", account_id);

        // 首先获取账号的Cookie
        let cookie_value = {
            let config = self.config.read().await;
            if let Some(account) = config.accounts.iter().find(|a| a.id == account_id) {
                if let Some(cookie) = &account.cookie {
                    cookie.value.clone()
                } else {
                    return Err("账号没有Cookie信息".to_string());
                }
            } else {
                return Err(format!("账号不存在: {}", account_id));
            }
        };

        // 调用API获取用户信息
        let user_info = self.get_user_info(&cookie_value).await?;

        // 更新账号状态和用户信息
        let (account_name, updated_config) = {
            let mut config = self.config.write().await;

            if let Some(account) = config.accounts.iter_mut().find(|a| a.id == account_id) {
                if let Some(cookie) = &mut account.cookie {
                    if user_info.is_valid {
                        cookie.update_validated_time();
                        account.update_status(AccountStatus::Active);

                        // 保存用户信息到metadata
                        if let Some(uid) = user_info.user_id {
                            account.metadata.insert("user_id".to_string(), uid);
                        }
                        if let Some(nick) = user_info.nickname {
                            // 同步更新账号名称与昵称保持一致
                            account.name = nick.clone();
                            account.metadata.insert("nickname".to_string(), nick);
                        }
                        if let Some(avatar_url) = user_info.avatar {
                            account.metadata.insert("avatar".to_string(), avatar_url);
                        }

                        println!("✅ Cookie验证成功，已更新用户信息");
                    } else {
                        account.update_status(AccountStatus::Inactive);
                        println!("❌ Cookie验证失败");
                    }

                    let name = account.name.clone();
                    config.last_updated = Some(Utc::now());
                    (name, config.clone())
                } else {
                    return Err("账号没有Cookie信息".to_string());
                }
            } else {
                return Err(format!("账号不存在: {}", account_id));
            }
        };

        // 发送事件
        let (event_type, event_message) = if user_info.is_valid {
            ("cookie_validated", "Cookie验证成功")
        } else {
            ("cookie_invalid", "Cookie验证失败")
        };
        self.emit_account_event(event_type, account_id, event_message)
            .await?;

        // 保存配置
        self.save_config(&updated_config).await?;

        println!(
            "🔍 Cookie验证完成: {} (有效: {})",
            account_name, user_info.is_valid
        );
        Ok(user_info.is_valid)
    }

    /// 获取账号统计信息
    pub async fn get_account_stats(&self) -> HashMap<String, serde_json::Value> {
        let config = self.config.read().await;
        let mut stats = HashMap::new();

        stats.insert(
            "total_accounts".to_string(),
            serde_json::Value::Number(config.accounts.len().into()),
        );
        stats.insert(
            "enabled_accounts".to_string(),
            serde_json::Value::Number(config.enabled_count().into()),
        );
        stats.insert(
            "available_accounts".to_string(),
            serde_json::Value::Number(config.available_count().into()),
        );

        let active_count = config
            .accounts
            .iter()
            .filter(|a| matches!(a.status, AccountStatus::Active))
            .count();
        stats.insert(
            "active_accounts".to_string(),
            serde_json::Value::Number(active_count.into()),
        );

        let error_count = config
            .accounts
            .iter()
            .filter(|a| matches!(a.status, AccountStatus::Error(_)))
            .count();
        stats.insert(
            "error_accounts".to_string(),
            serde_json::Value::Number(error_count.into()),
        );

        stats
    }

    /// 通过登录窗口添加新账号
    pub async fn add_account_via_login(
        &self,
        name: String,
        description: Option<String>,
    ) -> Result<Account, String> {
        println!("🔑 开始通过登录添加账号: {}", name);

        // 使用标准流程
        self.add_account_via_login_standard(name, description).await
    }

    /// 标准平台：通过登录窗口添加新账号（原有逻辑）
    async fn add_account_via_login_standard(
        &self,
        name: String,
        description: Option<String>,
    ) -> Result<Account, String> {
        println!("🍎 标准平台：开始通过登录添加账号: {}", name);

        // 创建账号
        let mut account = Account::new(name);
        if let Some(desc) = description {
            account.description = Some(desc);
        }

        // 创建登录会话（使用浏览器方式）
        let session_id = self
            .create_login_session(Some(format!("添加账号: {}", account.name)))
            .await?;

        println!("✅ 登录会话已创建: {}", session_id);

        // 保存会话ID到账号元数据
        account
            .metadata
            .insert("login_session_id".to_string(), session_id);

        // 添加账号到配置
        let updated_config = {
            let mut config = self.config.write().await;
            config.accounts.push(account.clone());
            config.last_updated = Some(Utc::now());
            config.clone()
        };

        // 保存配置
        self.save_config(&updated_config).await?;

        println!("✅ 账号已添加，等待用户完成浏览器登录: {}", account.name);
        Ok(account)
    }

    /// 清理失效账号（移除连续失败次数过多的账号）
    pub async fn cleanup_failed_accounts(&self, max_failures: u32) -> Result<Vec<String>, String> {
        let mut config = self.config.write().await;
        let initial_len = config.accounts.len();

        let removed_accounts: Vec<String> = config
            .accounts
            .iter()
            .filter(|a| a.stats.consecutive_failures >= max_failures)
            .map(|a| a.id.clone())
            .collect();

        config
            .accounts
            .retain(|a| a.stats.consecutive_failures < max_failures);

        if config.accounts.len() < initial_len {
            config.last_updated = Some(Utc::now());
            self.save_config(&config).await?;

            for account_id in &removed_accounts {
                self.emit_account_event(
                    "account_auto_removed",
                    account_id,
                    "账号因连续失败过多被自动移除",
                )
                .await?;
            }

            println!("🧹 已清理 {} 个失效账号", removed_accounts.len());
        }

        Ok(removed_accounts)
    }

    /// 清理无效的会话目录
    /// 检查并删除不再被任何账号引用的会话目录
    pub async fn cleanup_invalid_session_directories(&self) -> Result<(), String> {
        log::info!("AccountService::cleanup_invalid_session_directories - 开始清理无效会话目录");

        let login_sessions_dir = self.data_dir.join("login_sessions");

        // 如果会话目录不存在，直接返回
        if !login_sessions_dir.exists() {
            log::info!(
                "AccountService::cleanup_invalid_session_directories - 会话目录不存在，跳过清理"
            );
            return Ok(());
        }

        // 获取所有账号中引用的会话ID
        let referenced_session_ids = {
            let config = self.config.read().await;
            config
                .accounts
                .iter()
                .filter_map(|account| account.metadata.get("login_session_id").map(|s| s.clone()))
                .collect::<std::collections::HashSet<String>>()
        };

        log::debug!(
            "AccountService::cleanup_invalid_session_directories - 找到 {} 个被引用的会话ID",
            referenced_session_ids.len()
        );

        // 扫描会话目录
        let entries = std::fs::read_dir(&login_sessions_dir)
            .map_err(|e| format!("读取会话目录失败: {}", e))?;

        let mut deleted_count = 0;

        for entry in entries {
            let entry = entry.map_err(|e| format!("读取目录项失败: {}", e))?;
            let path = entry.path();

            if path.is_dir() {
                if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                    // 检查这个会话ID是否还被任何账号引用
                    if !referenced_session_ids.contains(dir_name) {
                        // 这是一个无效的会话目录，删除它
                        if let Err(e) = std::fs::remove_dir_all(&path) {
                            log::warn!("AccountService::cleanup_invalid_session_directories - 删除无效会话目录失败: {} ({})", path.display(), e);
                        } else {
                            deleted_count += 1;
                            log::debug!("AccountService::cleanup_invalid_session_directories - 删除无效会话目录: {}", path.display());
                        }
                    }
                }
            }
        }

        log::info!("AccountService::cleanup_invalid_session_directories - 清理完成，删除了 {} 个无效会话目录", deleted_count);
        Ok(())
    }
}

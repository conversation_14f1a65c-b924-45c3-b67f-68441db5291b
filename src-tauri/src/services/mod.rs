// 新的服务架构 - 完全解耦的服务模块
pub mod account;
pub mod auth;
pub mod config;
pub mod crypto;
pub mod monitor;
pub mod notification;
pub mod time;

// 重新导出服务
pub use account::service::AccountService;
pub use auth::service::AuthService;
pub use config::service::ConfigService;
pub use crypto::{CryptoService, EncryptedClient};
pub use monitor::service::MonitorService;
pub use notification::service::NotificationService;
pub use time::service::TimeService;

// 重新导出所有命令
pub use account::commands::*;
pub use auth::commands::*;
pub use config::commands::*;
pub use monitor::commands::*;
pub use notification::commands::*;
pub use time::commands::*;

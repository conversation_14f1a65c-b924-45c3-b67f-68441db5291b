use super::service::{ConfigFileInfo, ConfigService};
use crate::models::config::MonitorConfig;
use tauri::State;

/// 获取配置
#[tauri::command]
pub async fn config_get(config_service: State<'_, ConfigService>) -> Result<MonitorConfig, String> {
    Ok(config_service.get_config().await)
}

/// 更新配置
#[tauri::command]
pub async fn config_update(config_service: State<'_, ConfigService>, config: MonitorConfig) -> Result<(), String> {
    config_service.update_config(config).await
}

/// 重新加载配置文件
#[tauri::command]
pub async fn config_reload(config_service: State<'_, ConfigService>) -> Result<MonitorConfig, String> {
    config_service.reload_config().await
}

/// 获取配置文件信息
#[tauri::command]
pub async fn config_get_file_info(config_service: State<'_, ConfigService>) -> Result<ConfigFileInfo, String> {
    Ok(config_service.get_config_file_info())
}

/// 备份配置文件
#[tauri::command]
pub async fn config_backup(config_service: State<'_, ConfigService>) -> Result<String, String> {
    config_service.backup_config().await
}

/// 从备份恢复配置
#[tauri::command]
pub async fn config_restore(config_service: State<'_, ConfigService>, backup_path: String) -> Result<(), String> {
    config_service.restore_config(&backup_path).await
}

/// 重置为默认配置
#[tauri::command]
pub async fn config_reset_default(config_service: State<'_, ConfigService>) -> Result<(), String> {
    config_service.reset_to_default().await
}

/// 验证配置文件
#[tauri::command]
pub async fn config_validate(config_service: State<'_, ConfigService>) -> Result<bool, String> {
    config_service.validate_config_file().await
}

/// 兼容性命令
#[tauri::command]
pub fn get_config_file_path_info(config_service: State<'_, ConfigService>) -> String {
    let info = config_service.get_config_file_info();
    format!("配置文件路径: {}, 存在: {}", info.path, info.exists)
}

/// 兼容性命令
#[tauri::command]
pub async fn reload_config_from_file(config_service: State<'_, ConfigService>) -> Result<MonitorConfig, String> {
    config_service.reload_config().await
}

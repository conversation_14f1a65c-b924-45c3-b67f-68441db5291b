use super::service::ConfigService;
use crate::models::config::MonitorConfig;
use tauri::{AppHandle, Emitter};

/// 配置事件处理器
pub struct ConfigEventHandler {
    config_service: ConfigService,
}

impl ConfigEventHandler {
    pub fn new(config_service: ConfigService) -> Self {
        Self { config_service }
    }

    /// 发送配置更新事件
    pub async fn emit_config_updated(&self, config: &MonitorConfig) -> Result<(), String> {
        self.config_service
            .app_handle()
            .emit("config_updated", config)
            .map_err(|e| format!("发送配置更新事件失败: {}", e))
    }

    /// 发送配置加载事件
    pub async fn emit_config_loaded(&self, config: &MonitorConfig) -> Result<(), String> {
        self.config_service
            .app_handle()
            .emit("config_loaded", config)
            .map_err(|e| format!("发送配置加载事件失败: {}", e))
    }

    /// 发送配置错误事件
    pub async fn emit_config_error(&self, error: &str) -> Result<(), String> {
        self.config_service
            .app_handle()
            .emit("config_error", error)
            .map_err(|e| format!("发送配置错误事件失败: {}", e))
    }

    /// 发送配置验证事件
    pub async fn emit_config_validated(&self, is_valid: bool) -> Result<(), String> {
        self.config_service
            .app_handle()
            .emit("config_validated", is_valid)
            .map_err(|e| format!("发送配置验证事件失败: {}", e))
    }

    /// 发送配置备份事件
    pub async fn emit_config_backed_up(&self, backup_path: &str) -> Result<(), String> {
        self.config_service
            .app_handle()
            .emit("config_backed_up", backup_path)
            .map_err(|e| format!("发送配置备份事件失败: {}", e))
    }

    /// 发送配置恢复事件
    pub async fn emit_config_restored(&self) -> Result<(), String> {
        self.config_service
            .app_handle()
            .emit("config_restored", ())
            .map_err(|e| format!("发送配置恢复事件失败: {}", e))
    }
}

/// 配置事件监听器设置
pub async fn setup_config_listeners(app_handle: AppHandle, config_service: ConfigService) {
    let handler = ConfigEventHandler::new(config_service.clone());

    // 监听配置文件变化
    let app_handle_clone = app_handle.clone();
    let config_service_clone = config_service.clone();
    
    tokio::spawn(async move {
        // 这里可以设置文件系统监听器来监控配置文件变化
        // 当配置文件被外部修改时自动重新加载
    });
}

/// 便捷的配置事件发送函数
pub async fn emit_config_event(
    app_handle: &AppHandle,
    event_name: &str,
    payload: &MonitorConfig,
) -> Result<(), String> {
    app_handle
        .emit(event_name, payload)
        .map_err(|e| format!("发送配置事件失败: {}", e))
}

/// 发送配置状态事件
pub async fn emit_config_status(
    app_handle: &AppHandle,
    status: &str,
    message: &str,
) -> Result<(), String> {
    let payload = serde_json::json!({
        "status": status,
        "message": message,
        "timestamp": chrono::Utc::now().to_rfc3339()
    });
    
    app_handle
        .emit("config_status", &payload)
        .map_err(|e| format!("发送配置状态事件失败: {}", e))
}

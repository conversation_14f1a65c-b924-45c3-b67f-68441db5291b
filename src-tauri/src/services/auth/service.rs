use crate::models::config::{ActivationData, RsaActivationInfo};
use crate::services::config::ConfigService;
use crate::utils::crypto::*;
use rsa::pss::VerifyingKey;
use rsa::signature::Verifier;
use rsa::{pkcs8::DecodePublicKey, RsaPublicKey};
use serde::{Deserialize, Serialize};
use sha2::Sha256;
use std::collections::HashMap;
use std::sync::Arc;
use sysinfo::{Networks, System};
use tauri::{AppHandle, Emitter};
use tokio::sync::RwLock;

/// 编译时读取RSA公钥文件（用于验证激活码）
/// 使用正斜杠路径，Rust 在所有平台上都能正确处理
const RSA_PUBLIC_KEY_PEM: &str = include_str!(concat!(env!("CARGO_MANIFEST_DIR"), "/keys/public_key.pem"));

/// 设备信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub cpu_id: String,
    pub motherboard_serial: String,
    pub mac_addresses: Vec<String>,
    pub system_uuid: String,
    pub hostname: String,
    pub disk_serials: Vec<String>,
    pub bios_info: String,
}

/// 激活信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivationInfo {
    pub device_fingerprint: String,
    pub user_id: String,
    pub expire_timestamp: i64,
    pub issued_timestamp: i64,
    pub features: Vec<String>,
    pub is_activated: bool,
}

impl Default for ActivationInfo {
    fn default() -> Self {
        Self { device_fingerprint: String::new(), user_id: String::new(), expire_timestamp: 0, issued_timestamp: 0, features: vec![], is_activated: false }
    }
}

/// Cookie信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CookieInfo {
    pub value: String,
    pub domain: String,
    pub path: String,
    pub expires: Option<i64>,
    pub secure: bool,
    pub http_only: bool,
}

/// 纯粹的认证服务 - 完全解耦，处理登录、激活、设备指纹等功能
#[derive(Clone)]
pub struct AuthService {
    app_handle: AppHandle,
    config_service: ConfigService,
    device_info: Arc<RwLock<Option<DeviceInfo>>>,
    activation_info: Arc<RwLock<ActivationInfo>>,
    cookies: Arc<RwLock<HashMap<String, CookieInfo>>>,
}

impl AuthService {
    /// 创建新的认证服务实例
    pub fn new(app_handle: AppHandle, config_service: ConfigService) -> Self {
        Self { app_handle, config_service, device_info: Arc::new(RwLock::new(None)), activation_info: Arc::new(RwLock::new(ActivationInfo::default())), cookies: Arc::new(RwLock::new(HashMap::new())) }
    }

    /// 获取app_handle的引用
    pub fn app_handle(&self) -> &AppHandle {
        &self.app_handle
    }

    /// 获取设备信息
    pub async fn get_device_info(&self) -> Result<DeviceInfo, String> {
        // 检查缓存
        {
            let cached_info = self.device_info.read().await;
            if let Some(info) = cached_info.as_ref() {
                return Ok(info.clone());
            }
        }

        // 收集设备信息
        let mut sys = System::new_all();
        sys.refresh_all();

        let cpu_id = self.get_cpu_id(&sys).await?;
        let motherboard_serial = self.get_motherboard_serial().await?;
        let mac_addresses = self.get_mac_addresses(&sys).await?;
        let system_uuid = self.get_system_uuid().await?;
        let hostname = System::host_name().unwrap_or_else(|| "unknown".to_string());
        let disk_serials = self.get_disk_serials().await?;
        let bios_info = self.get_bios_info().await?;

        let device_info = DeviceInfo { cpu_id, motherboard_serial, mac_addresses, system_uuid, hostname, disk_serials, bios_info };

        // 缓存设备信息
        {
            let mut cached_info = self.device_info.write().await;
            *cached_info = Some(device_info.clone());
        }

        Ok(device_info)
    }

    /// 生成设备指纹（使用稳定的硬件信息，排除易变的hostname）
    pub async fn generate_device_fingerprint(&self) -> Result<String, String> {
        let device_info = self.get_device_info().await?;

        // 只使用稳定的硬件信息生成指纹，排除hostname
        // 优先级：主板序列号 > 系统UUID > CPU信息 > MAC地址
        let mut stable_components = Vec::new();

        // 主板序列号（最稳定）
        if !device_info.motherboard_serial.is_empty() && !device_info.motherboard_serial.contains("unknown") {
            stable_components.push(format!("mb:{}", device_info.motherboard_serial));
        }

        // 系统UUID（很稳定）
        if !device_info.system_uuid.is_empty() && !device_info.system_uuid.contains("unknown") {
            stable_components.push(format!("uuid:{}", device_info.system_uuid));
        }

        // CPU信息（相对稳定）
        if !device_info.cpu_id.is_empty() && device_info.cpu_id != "unknown" {
            stable_components.push(format!("cpu:{}", device_info.cpu_id));
        }

        // MAC地址（中等稳定，只使用第一个物理网卡）
        if !device_info.mac_addresses.is_empty() {
            // 过滤出物理网卡MAC地址
            let physical_macs: Vec<String> = device_info
                .mac_addresses
                .iter()
                .filter(|mac| !mac.contains("00:00:00:00:00:00") && !mac.contains("lo") && !mac.contains("docker") && !mac.contains("veth") && !mac.contains("br-") && !mac.contains("virtual"))
                .cloned()
                .collect();

            if !physical_macs.is_empty() {
                stable_components.push(format!("mac:{}", physical_macs[0]));
            }
        }

        // 确保至少有一个稳定组件
        if stable_components.is_empty() {
            return Err("无法获取足够的稳定硬件信息生成设备指纹".to_string());
        }

        let fingerprint_data = stable_components.join("|");
        Ok(generate_sha256(&fingerprint_data))
    }

    /// 生成加密的设备ID
    pub async fn generate_encrypted_device_id(&self) -> Result<String, String> {
        let fingerprint = self.generate_device_fingerprint().await?;

        // 使用固定密钥加密（实际应用中应该使用更安全的密钥管理）
        let key = b"goldfish_app_device_key_32bytes!";
        let iv = b"goldfish_iv_16b!";

        simple_aes_encrypt(&fingerprint, key, iv)
    }

    /// 验证激活码并保存到配置
    pub async fn validate_activation_code(&self, activation_code: &str) -> Result<serde_json::Value, String> {
        println!("🔧 开始激活流程");
        println!("📋 激活码长度: {}", activation_code.len());

        // 获取当前设备指纹
        let device_fingerprint = self.generate_device_fingerprint().await?;
        println!("🔍 设备指纹: {}", device_fingerprint);

        // 解码激活码
        let decoded_data = base64_decode(activation_code)?;
        let json_string = String::from_utf8(decoded_data).map_err(|e| format!("激活码UTF-8解码失败: {}", e))?;

        let signed_data: serde_json::Value = serde_json::from_str(&json_string).map_err(|e| format!("激活码JSON解析失败: {}", e))?;

        // 提取数据和签名
        let data = signed_data["data"].as_str().ok_or("激活码格式错误：缺少数据字段")?;
        let signature_b64 = signed_data["signature"].as_str().ok_or("激活码格式错误：缺少签名字段")?;

        // RSA公钥验证签名
        self.verify_rsa_signature(data, signature_b64).await?;

        // 解析激活数据
        let activation_data: ActivationData = serde_json::from_str(data).map_err(|e| format!("激活数据解析失败: {}", e))?;

        // 验证设备指纹
        if activation_data.device_fingerprint != device_fingerprint {
            return Err("激活码与当前设备不匹配".to_string());
        }

        // 验证过期时间
        let now = chrono::Utc::now().timestamp();
        if now > activation_data.expire_timestamp {
            return Err("激活码已过期".to_string());
        }

        // 保存激活信息到配置
        self.save_activation_to_config(activation_code, &activation_data).await?;

        // 更新内存中的激活信息
        {
            let mut current_activation = self.activation_info.write().await;
            *current_activation = ActivationInfo {
                device_fingerprint: activation_data.device_fingerprint.clone(),
                user_id: activation_data.user_id.clone(),
                expire_timestamp: activation_data.expire_timestamp,
                issued_timestamp: activation_data.issued_timestamp,
                features: activation_data.features.clone(),
                is_activated: true,
            };
        }

        // 发送激活成功事件
        self.emit_activation_success(&ActivationInfo {
            device_fingerprint: activation_data.device_fingerprint.clone(),
            user_id: activation_data.user_id.clone(),
            expire_timestamp: activation_data.expire_timestamp,
            issued_timestamp: activation_data.issued_timestamp,
            features: activation_data.features.clone(),
            is_activated: true,
        })
        .await?;

        Ok(serde_json::json!({
            "success": true,
            "message": "激活成功",
            "user_id": activation_data.user_id,
            "expire_timestamp": activation_data.expire_timestamp,
            "features": activation_data.features
        }))
    }

    /// 使用RSA公钥验证签名
    async fn verify_rsa_signature(&self, data: &str, signature_b64: &str) -> Result<(), String> {
        use base64::{engine::general_purpose, Engine as _};
        use rsa::pss::VerifyingKey;
        use rsa::signature::Verifier;
        use sha2::Sha256;

        // 解码签名
        let signature_bytes = general_purpose::STANDARD.decode(signature_b64).map_err(|e| format!("签名Base64解码失败: {}", e))?;

        // 使用硬编码公钥
        let public_key = RsaPublicKey::from_public_key_pem(RSA_PUBLIC_KEY_PEM).map_err(|e| format!("解析RSA公钥失败: {}", e))?;

        // 创建PSS验证器，使用最大盐长度（与Python一致）
        // PSS.MAX_LENGTH 在Python中对应 Rust 中的最大可能盐长度
        let verifying_key = VerifyingKey::<Sha256>::new(public_key);

        // 创建签名对象
        let signature = rsa::pss::Signature::try_from(signature_bytes.as_slice()).map_err(|e| format!("创建签名对象失败: {}", e))?;

        // 验证签名
        verifying_key.verify(data.as_bytes(), &signature).map_err(|e| format!("RSA签名验证失败: {}", e))?;

        Ok(())
    }

    /// 保存激活信息到配置文件
    async fn save_activation_to_config(&self, activation_code: &str, _activation_data: &ActivationData) -> Result<(), String> {
        // 获取当前配置
        let mut config = self.config_service.get_config().await;

        // 只保存激活码
        config.rsa_activation_info.set_activation_code(activation_code.to_string());

        // 保存配置
        self.config_service.save_config(&config).await?;

        Ok(())
    }

    /// 检查激活状态（从配置文件加载并验证）
    pub async fn check_activation_status(&self) -> Result<bool, String> {
        // 从配置文件加载激活信息
        let config = self.config_service.get_config().await;
        let activation_info = &config.rsa_activation_info;

        // 检查是否有激活码
        let activation_code = match &activation_info.activation_code {
            Some(code) => code,
            None => return Ok(false),
        };

        // 重新验证激活码（确保未被篡改）
        match self.validate_stored_activation_code(activation_code).await {
            Ok(activation_data) => {
                // 更新内存中的激活状态
                {
                    let mut current_activation = self.activation_info.write().await;
                    *current_activation = ActivationInfo {
                        device_fingerprint: activation_data.device_fingerprint,
                        user_id: activation_data.user_id,
                        expire_timestamp: activation_data.expire_timestamp,
                        issued_timestamp: activation_data.issued_timestamp,
                        features: activation_data.features,
                        is_activated: true,
                    };
                }
                Ok(true)
            }
            Err(e) => {
                // 激活码验证失败，清除激活状态
                self.clear_activation().await?;
                Err(format!("激活码验证失败: {}", e))
            }
        }
    }

    /// 验证存储的激活码（不保存，只验证）
    async fn validate_stored_activation_code(&self, activation_code: &str) -> Result<ActivationData, String> {
        // 获取当前设备指纹
        let device_fingerprint = self.generate_device_fingerprint().await?;

        // 解码激活码
        let decoded_data = base64_decode(activation_code)?;
        let json_string = String::from_utf8(decoded_data).map_err(|e| format!("激活码UTF-8解码失败: {}", e))?;

        let signed_data: serde_json::Value = serde_json::from_str(&json_string).map_err(|e| format!("激活码JSON解析失败: {}", e))?;

        // 提取数据和签名
        let data = signed_data["data"].as_str().ok_or("激活码格式错误：缺少数据字段")?;
        let signature_b64 = signed_data["signature"].as_str().ok_or("激活码格式错误：缺少签名字段")?;

        // RSA公钥验证签名
        self.verify_rsa_signature(data, signature_b64).await?;

        // 解析激活数据
        let activation_data: ActivationData = serde_json::from_str(data).map_err(|e| format!("激活数据解析失败: {}", e))?;

        // 验证设备指纹
        if activation_data.device_fingerprint != device_fingerprint {
            return Err("激活码与当前设备不匹配".to_string());
        }

        // 验证过期时间
        let now = chrono::Utc::now().timestamp();
        if now > activation_data.expire_timestamp {
            return Err("激活码已过期".to_string());
        }

        Ok(activation_data)
    }

    /// 清除激活状态
    pub async fn clear_activation(&self) -> Result<(), String> {
        // 清除配置中的激活信息
        let mut config = self.config_service.get_config().await;
        config.rsa_activation_info.clear();
        self.config_service.save_config(&config).await?;

        // 清除内存中的激活信息
        {
            let mut current_activation = self.activation_info.write().await;
            *current_activation = ActivationInfo::default();
        }

        Ok(())
    }

    /// 获取激活信息
    pub async fn get_activation_info(&self) -> ActivationInfo {
        let activation_info = self.activation_info.read().await;
        activation_info.clone()
    }

    /// 设置Cookie
    pub async fn set_cookie(&self, name: &str, cookie_info: CookieInfo) -> Result<(), String> {
        let mut cookies = self.cookies.write().await;
        cookies.insert(name.to_string(), cookie_info);

        // 发送Cookie更新事件
        self.emit_cookie_updated(name).await?;

        Ok(())
    }

    /// 获取Cookie
    pub async fn get_cookie(&self, name: &str) -> Option<CookieInfo> {
        let cookies = self.cookies.read().await;
        cookies.get(name).cloned()
    }

    /// 清除Cookie
    pub async fn clear_cookie(&self, name: &str) -> Result<(), String> {
        let mut cookies = self.cookies.write().await;
        cookies.remove(name);

        // 发送Cookie清除事件
        self.emit_cookie_cleared(name).await?;

        Ok(())
    }

    /// 清除所有Cookie
    pub async fn clear_all_cookies(&self) -> Result<(), String> {
        let mut cookies = self.cookies.write().await;
        cookies.clear();

        // 发送所有Cookie清除事件
        self.emit_all_cookies_cleared().await?;

        Ok(())
    }

    /// 检查Cookie是否存在
    pub async fn has_cookie(&self, name: &str) -> bool {
        let cookies = self.cookies.read().await;
        cookies.contains_key(name)
    }

    /// 获取所有Cookie
    pub async fn get_all_cookies(&self) -> HashMap<String, CookieInfo> {
        let cookies = self.cookies.read().await;
        cookies.clone()
    }

    // 私有方法：获取CPU ID
    async fn get_cpu_id(&self, sys: &System) -> Result<String, String> {
        if let Some(cpu) = sys.cpus().first() {
            Ok(cpu.brand().to_string())
        } else {
            Err("无法获取CPU信息".to_string())
        }
    }

    // 私有方法：获取主板序列号
    async fn get_motherboard_serial(&self) -> Result<String, String> {
        #[cfg(target_os = "windows")]
        {
            self.get_windows_motherboard_serial().await
        }

        #[cfg(target_os = "macos")]
        {
            self.get_macos_motherboard_serial().await
        }

        #[cfg(target_os = "linux")]
        {
            self.get_linux_motherboard_serial().await
        }

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            Ok("unknown_motherboard".to_string())
        }
    }

    // 私有方法：获取MAC地址
    async fn get_mac_addresses(&self, sys: &System) -> Result<Vec<String>, String> {
        let networks = Networks::new_with_refreshed_list();
        let mut mac_addresses: Vec<String> = networks
            .iter()
            .filter_map(|(_, network)| {
                let mac = network.mac_address();
                // 检查是否为全零MAC地址
                let is_zero = mac.0.iter().all(|&b| b == 0);
                if is_zero {
                    None
                } else {
                    Some(format!("{:02x}:{:02x}:{:02x}:{:02x}:{:02x}:{:02x}", mac.0[0], mac.0[1], mac.0[2], mac.0[3], mac.0[4], mac.0[5]))
                }
            })
            .collect();

        // 排序确保顺序一致
        mac_addresses.sort();

        if mac_addresses.is_empty() {
            Err("无法获取MAC地址".to_string())
        } else {
            Ok(mac_addresses)
        }
    }

    // 私有方法：获取系统UUID
    async fn get_system_uuid(&self) -> Result<String, String> {
        // 这里应该实现获取系统UUID的逻辑
        Ok("system_uuid_placeholder".to_string())
    }

    // 私有方法：获取磁盘序列号
    async fn get_disk_serials(&self) -> Result<Vec<String>, String> {
        // 这里应该实现获取磁盘序列号的逻辑
        let mut disk_serials = vec!["disk_serial_placeholder".to_string()];
        // 排序确保顺序一致
        disk_serials.sort();
        Ok(disk_serials)
    }

    // 私有方法：获取BIOS信息
    async fn get_bios_info(&self) -> Result<String, String> {
        // 这里应该实现获取BIOS信息的逻辑
        Ok("bios_info_placeholder".to_string())
    }

    // 平台特定的主板序列号获取方法
    #[cfg(target_os = "windows")]
    async fn get_windows_motherboard_serial(&self) -> Result<String, String> {
        use crate::utils::command::execute_command_string;
        execute_command_string("wmic", &["baseboard", "get", "serialnumber", "/value"])
            .map(|output| output.lines().find(|line| line.starts_with("SerialNumber=")).and_then(|line| line.split('=').nth(1)).unwrap_or("unknown").trim().to_string())
    }

    #[cfg(target_os = "macos")]
    async fn get_macos_motherboard_serial(&self) -> Result<String, String> {
        use crate::utils::command::execute_command_string;
        execute_command_string("system_profiler", &["SPHardwareDataType"])
            .map(|output| output.lines().find(|line| line.contains("Serial Number")).and_then(|line| line.split(':').nth(1)).unwrap_or("unknown").trim().to_string())
    }

    #[cfg(target_os = "linux")]
    async fn get_linux_motherboard_serial(&self) -> Result<String, String> {
        use crate::utils::command::execute_command_string;
        execute_command_string("dmidecode", &["-s", "baseboard-serial-number"]).or_else(|_| execute_command_string("cat", &["/sys/class/dmi/id/board_serial"])).map(|output| output.trim().to_string())
    }

    // 事件发送方法
    async fn emit_activation_success(&self, activation_info: &ActivationInfo) -> Result<(), String> {
        self.app_handle.emit("activation_success", activation_info).map_err(|e| format!("发送激活成功事件失败: {}", e))
    }

    async fn emit_cookie_updated(&self, name: &str) -> Result<(), String> {
        self.app_handle.emit("cookie_updated", name).map_err(|e| format!("发送Cookie更新事件失败: {}", e))
    }

    async fn emit_cookie_cleared(&self, name: &str) -> Result<(), String> {
        self.app_handle.emit("cookie_cleared", name).map_err(|e| format!("发送Cookie清除事件失败: {}", e))
    }

    async fn emit_all_cookies_cleared(&self) -> Result<(), String> {
        self.app_handle.emit("all_cookies_cleared", ()).map_err(|e| format!("发送所有Cookie清除事件失败: {}", e))
    }
}

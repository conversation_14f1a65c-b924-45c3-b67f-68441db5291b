use super::service::{ActivationInfo, AuthService};
use tauri::{AppHandle, Emitter};

/// 认证事件处理器
pub struct AuthEventHandler {
    auth_service: AuthService,
}

impl AuthEventHandler {
    pub fn new(auth_service: AuthService) -> Self {
        Self { auth_service }
    }

    /// 发送激活成功事件
    pub async fn emit_activation_success(&self, activation_info: &ActivationInfo) -> Result<(), String> {
        self.auth_service.app_handle().emit("activation_success", activation_info).map_err(|e| format!("发送激活成功事件失败: {}", e))
    }

    /// 发送激活失败事件
    pub async fn emit_activation_failed(&self, error: &str) -> Result<(), String> {
        self.auth_service.app_handle().emit("activation_failed", error).map_err(|e| format!("发送激活失败事件失败: {}", e))
    }

    /// 发送Cookie更新事件
    pub async fn emit_cookie_updated(&self, name: &str) -> Result<(), String> {
        self.auth_service.app_handle().emit("cookie_updated", name).map_err(|e| format!("发送Cookie更新事件失败: {}", e))
    }

    /// 发送设备信息更新事件
    pub async fn emit_device_info_updated(&self) -> Result<(), String> {
        self.auth_service.app_handle().emit("device_info_updated", ()).map_err(|e| format!("发送设备信息更新事件失败: {}", e))
    }
}

/// 认证事件监听器设置
pub async fn setup_auth_listeners(app_handle: AppHandle, auth_service: AuthService) {
    let handler = AuthEventHandler::new(auth_service.clone());

    // 监听认证相关事件
    let app_handle_clone = app_handle.clone();
    let auth_service_clone = auth_service.clone();

    tokio::spawn(async move {
        // 这里可以设置认证事件监听器
        // 例如监听激活状态变化、Cookie过期等
    });
}

/// 便捷的认证事件发送函数
pub async fn emit_auth_event(app_handle: &AppHandle, event_name: &str, payload: &serde_json::Value) -> Result<(), String> {
    app_handle.emit(event_name, payload).map_err(|e| format!("发送认证事件失败: {}", e))
}

/// 发送认证错误事件
pub async fn emit_auth_error(app_handle: &AppHandle, error: &str) -> Result<(), String> {
    let payload = serde_json::json!({
        "error": error,
        "timestamp": chrono::Utc::now().to_rfc3339()
    });

    app_handle.emit("auth_error", &payload).map_err(|e| format!("发送认证错误事件失败: {}", e))
}

// 移除RSA激活相关导入，使用简化的激活信息
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct RsaActivationInfo {
    pub activation_code: Option<String>, // 只存储激活码，其他信息从激活码解析
}

// 关键词价格规则结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeywordPriceRule {
    pub keyword: String,
    pub min_price: f64,
    pub max_price: f64,
}

// 拉黑卖家结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockedSeller {
    pub seller_name: String, // 卖家名称
    pub seller_id: String,   // 卖家ID
}

// 省份选择结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProvinceInfo {
    pub id: i32,
    pub name: String,
    pub level: Option<String>,
    pub has_child: bool,
}

// 监控数据项结构
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MonitorItem {
    // 基础信息
    pub item_id: String,
    pub title: String,
    pub price: String,
    pub time: String,

    // 商品详细信息 (从 clickParam.args 获取)
    pub publish_time: String,
    pub seller_id: String,
    pub cat_id: String,

    // 扩展信息 (从 exContent 获取)
    pub area: String,
    pub user_nick_name: String,
    pub pic_url: String,

    // 跳转链接 (从 targetUrl 获取)
    pub target_url: String,

    // 匹配的关键词列表
    pub matched_keywords: Vec<String>,
}

// 监控配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorConfig {
    pub interval_seconds: u64,
    pub target_page_count: u32,
    pub keywords: Vec<String>,
    pub exclude_keywords: Vec<String>,
    pub min_price: f64,
    pub max_price: f64,
    pub notify_enabled: bool,
    // 新增：关键词价格规则数组（优先使用）
    #[serde(default)]
    pub keyword_price_rules: Vec<KeywordPriceRule>,
    // 新增：钉钉推送开关
    #[serde(default = "default_dingtalk_enabled")]
    pub dingtalk_enabled: bool,
    // 新增：钉钉推送hook列表
    #[serde(default)]
    pub dingtalk_hooks: Vec<String>,
    // 新增：RSA激活信息
    #[serde(default)]
    pub rsa_activation_info: RsaActivationInfo,
    // 新增：前端显示数据条数限制
    #[serde(default = "default_display_limit")]
    pub display_limit: u32,
    // 新增：拉黑卖家列表
    #[serde(default)]
    pub blocked_sellers: Vec<BlockedSeller>,
    // 新增：选中的省份列表
    #[serde(default)]
    pub selected_provinces: Vec<ProvinceInfo>,
}

// 默认显示限制函数
fn default_display_limit() -> u32 {
    30
}

// 默认钉钉推送开关函数
fn default_dingtalk_enabled() -> bool {
    true
}

impl Default for MonitorConfig {
    fn default() -> Self {
        Self {
            interval_seconds: 10,
            target_page_count: 5,
            keywords: vec!["相机".to_string()],
            exclude_keywords: vec![],
            min_price: 0.0,
            max_price: 99999.0,
            notify_enabled: true,
            keyword_price_rules: vec![],
            dingtalk_enabled: true,
            dingtalk_hooks: vec![],
            rsa_activation_info: RsaActivationInfo::default(),
            display_limit: 30,
            blocked_sellers: vec![],
            selected_provinces: vec![],
        }
    }
}

impl MonitorConfig {
    pub fn validate(&self) -> Result<(), String> {
        // 验证基本参数
        if self.interval_seconds < 1 {
            return Err("刷新间隔不能小于1秒".to_string());
        }

        if self.target_page_count == 0 {
            return Err("目标页数不能为0".to_string());
        }

        // 验证显示限制
        if self.display_limit < 1 || self.display_limit > 500 {
            return Err("显示条数限制必须在1-500之间".to_string());
        }

        // 验证关键词价格规则
        if !self.keyword_price_rules.is_empty() {
            for rule in &self.keyword_price_rules {
                if rule.keyword.trim().is_empty() {
                    return Err("关键词不能为空".to_string());
                }
                if rule.min_price < 0.0 {
                    return Err(format!("关键词 {} 的最低价格不能小于0", rule.keyword));
                }
                if rule.max_price < rule.min_price {
                    return Err(format!(
                        "关键词 {} 的最高价格不能小于最低价格",
                        rule.keyword
                    ));
                }
            }
        } else {
            // 如果没有规则，验证传统的全局价格设置
            if self.min_price < 0.0 {
                return Err("最低价格不能小于0".to_string());
            }

            if self.max_price < self.min_price {
                return Err("最高价格不能小于最低价格".to_string());
            }
        }

        Ok(())
    }

    pub fn has_cookie(&self) -> bool {
        // 多账号模式下，此方法用于基础验证
        // 实际的账号检查在 AccountService 中进行
        // 这里返回 true 表示配置本身有效，具体账号检查由业务层处理
        true
    }

    // 获取有效的关键词列表（优先从规则中获取）
    pub fn get_keywords(&self) -> Vec<String> {
        if !self.keyword_price_rules.is_empty() {
            self.keyword_price_rules
                .iter()
                .map(|rule| rule.keyword.clone())
                .collect()
        } else {
            self.keywords.clone()
        }
    }

    // 根据关键词获取对应的价格范围
    pub fn get_price_range_for_keyword(&self, keyword: &str) -> (f64, f64) {
        if let Some(rule) = self
            .keyword_price_rules
            .iter()
            .find(|r| r.keyword == keyword)
        {
            (rule.min_price, rule.max_price)
        } else {
            // 回退到全局价格设置
            (self.min_price, self.max_price)
        }
    }

    // RSA激活相关方法
    pub fn is_activated(&self) -> bool {
        self.rsa_activation_info.activation_code.is_some()
    }

    pub fn update_rsa_activation_info(&mut self, rsa_activation_info: RsaActivationInfo) {
        self.rsa_activation_info = rsa_activation_info;
    }

    pub fn get_rsa_activation_info(&self) -> &RsaActivationInfo {
        &self.rsa_activation_info
    }
}

impl RsaActivationInfo {
    /// 检查激活是否有效（只检查是否有激活码）
    pub fn is_valid(&self) -> bool {
        self.activation_code.is_some()
    }

    /// 设置激活码
    pub fn set_activation_code(&mut self, activation_code: String) {
        self.activation_code = Some(activation_code);
    }

    /// 清除激活信息
    pub fn clear(&mut self) {
        self.activation_code = None;
    }
}

/// 激活数据结构（从激活码解析出来的数据）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivationData {
    pub device_fingerprint: String,
    pub user_id: String,
    pub expire_timestamp: i64,
    pub issued_timestamp: i64,
    pub features: Vec<String>,
}

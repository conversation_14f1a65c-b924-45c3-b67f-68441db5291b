use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// 账号状态枚举
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AccountStatus {
    /// 活跃状态 - Cookie有效，可以正常使用
    Active,
    /// 失效状态 - Cookie过期或无效
    Inactive,
    /// 使用中状态 - 正在进行API调用
    InUse,
    /// 错误状态 - 出现错误，暂时不可用
    Error(String),
}

impl Default for AccountStatus {
    fn default() -> Self {
        AccountStatus::Inactive
    }
}

/// 账号使用统计信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AccountStats {
    /// 总调用次数
    pub total_calls: u64,
    /// 成功调用次数
    pub success_calls: u64,
    /// 失败调用次数
    pub failed_calls: u64,
    /// 最后使用时间
    pub last_used_at: Option<DateTime<Utc>>,
    /// 最后成功时间
    pub last_success_at: Option<DateTime<Utc>>,
    /// 最后失败时间
    pub last_failed_at: Option<DateTime<Utc>>,
    /// 连续失败次数
    pub consecutive_failures: u32,
}

/// Cookie信息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountCookie {
    /// Cookie值
    pub value: String,
    /// 域名
    pub domain: String,
    /// 路径
    pub path: String,
    /// 过期时间（时间戳）
    pub expires: Option<i64>,
    /// 是否安全连接
    pub secure: bool,
    /// 是否仅HTTP
    pub http_only: bool,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后验证时间
    pub last_validated_at: Option<DateTime<Utc>>,
}

impl AccountCookie {
    /// 创建新的Cookie信息
    pub fn new(value: String, domain: String) -> Self {
        Self {
            value,
            domain,
            path: "/".to_string(),
            expires: None,
            secure: false,
            http_only: false,
            created_at: Utc::now(),
            last_validated_at: None,
        }
    }

    /// 检查Cookie是否过期
    pub fn is_expired(&self) -> bool {
        if let Some(expires) = self.expires {
            let now = Utc::now().timestamp();
            expires <= now
        } else {
            false
        }
    }

    /// 更新最后验证时间
    pub fn update_validated_time(&mut self) {
        self.last_validated_at = Some(Utc::now());
    }
}

/// 账号实体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Account {
    /// 账号唯一ID
    pub id: String,
    /// 账号名称（用户自定义）
    pub name: String,
    /// 账号描述
    pub description: Option<String>,
    /// Cookie信息
    pub cookie: Option<AccountCookie>,
    /// 账号状态
    pub status: AccountStatus,
    /// 使用统计
    pub stats: AccountStats,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 是否启用
    pub enabled: bool,
    /// 优先级（数字越小优先级越高）
    pub priority: u32,
    /// 扩展属性
    pub metadata: HashMap<String, String>,
}

impl Account {
    /// 创建新账号
    pub fn new(name: String) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name,
            description: None,
            cookie: None,
            status: AccountStatus::Inactive,
            stats: AccountStats::default(),
            created_at: now,
            updated_at: now,
            enabled: true,
            priority: 100,
            metadata: HashMap::new(),
        }
    }

    /// 设置Cookie
    pub fn set_cookie(&mut self, cookie: AccountCookie) {
        self.cookie = Some(cookie);
        self.updated_at = Utc::now();
        // 设置Cookie后，如果之前是失效状态，改为活跃状态
        if self.status == AccountStatus::Inactive {
            self.status = AccountStatus::Active;
        }
    }

    /// 清除Cookie
    pub fn clear_cookie(&mut self) {
        self.cookie = None;
        self.status = AccountStatus::Inactive;
        self.updated_at = Utc::now();
    }

    /// 更新状态
    pub fn update_status(&mut self, status: AccountStatus) {
        self.status = status;
        self.updated_at = Utc::now();
    }

    /// 记录调用统计
    pub fn record_call(&mut self, success: bool) {
        self.stats.total_calls += 1;
        self.stats.last_used_at = Some(Utc::now());

        if success {
            self.stats.success_calls += 1;
            self.stats.last_success_at = Some(Utc::now());
            self.stats.consecutive_failures = 0;
            // 成功调用后，如果状态是错误，恢复为活跃
            if matches!(self.status, AccountStatus::Error(_)) {
                self.status = AccountStatus::Active;
            }
        } else {
            self.stats.failed_calls += 1;
            self.stats.last_failed_at = Some(Utc::now());
            self.stats.consecutive_failures += 1;
        }

        self.updated_at = Utc::now();
    }

    /// 检查账号是否可用
    pub fn is_available(&self) -> bool {
        self.enabled
            && self.cookie.is_some()
            && matches!(self.status, AccountStatus::Active)
            && !self.cookie.as_ref().unwrap().is_expired()
    }

    /// 检查是否需要验证Cookie
    pub fn needs_validation(&self) -> bool {
        if let Some(cookie) = &self.cookie {
            if let Some(last_validated) = cookie.last_validated_at {
                // 超过1小时未验证则需要重新验证
                let now = Utc::now();
                (now - last_validated).num_hours() >= 1
            } else {
                true // 从未验证过
            }
        } else {
            false // 没有Cookie不需要验证
        }
    }

    /// 获取Cookie字符串
    pub fn get_cookie_string(&self) -> Option<String> {
        self.cookie.as_ref().map(|c| c.value.clone())
    }

    /// 设置优先级
    pub fn set_priority(&mut self, priority: u32) {
        self.priority = priority;
        self.updated_at = Utc::now();
    }

    /// 启用/禁用账号
    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
        self.updated_at = Utc::now();
        if !enabled {
            self.status = AccountStatus::Inactive;
        }
    }
}

/// 账号列表配置
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AccountConfig {
    /// 账号列表
    pub accounts: Vec<Account>,
    /// 轮询策略配置
    pub rotation_config: RotationConfig,
    /// 最后更新时间
    pub last_updated: Option<DateTime<Utc>>,
}

/// 轮询策略配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RotationConfig {
    /// 轮询策略类型
    pub strategy: RotationStrategy,
    /// 每个账号的最小间隔时间（秒）
    pub min_interval_per_account: u64,
    /// 最大连续失败次数（超过后暂时跳过该账号）
    pub max_consecutive_failures: u32,
    /// 失败账号的冷却时间（秒）
    pub failure_cooldown_seconds: u64,
}

impl Default for RotationConfig {
    fn default() -> Self {
        Self {
            strategy: RotationStrategy::RoundRobin,
            min_interval_per_account: 30,  // 每个账号最少间隔30秒
            max_consecutive_failures: 3,   // 最多连续失败3次
            failure_cooldown_seconds: 300, // 失败后冷却5分钟
        }
    }
}

/// 轮询策略枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RotationStrategy {
    /// 轮询策略 - 按顺序轮流使用
    RoundRobin,
    /// 优先级策略 - 按优先级使用
    Priority,
    /// 随机策略 - 随机选择可用账号
    Random,
}

impl AccountConfig {
    /// 验证配置
    pub fn validate(&self) -> Result<(), String> {
        // 允许空账号列表，这在初始化时是正常的
        if !self.accounts.is_empty() {
            // 检查账号ID唯一性
            let mut ids = std::collections::HashSet::new();
            for account in &self.accounts {
                if !ids.insert(&account.id) {
                    return Err(format!("账号ID重复: {}", account.id));
                }
            }
        }

        Ok(())
    }

    /// 获取可用账号数量
    pub fn available_count(&self) -> usize {
        self.accounts.iter().filter(|a| a.is_available()).count()
    }

    /// 获取启用账号数量
    pub fn enabled_count(&self) -> usize {
        self.accounts.iter().filter(|a| a.enabled).count()
    }
}

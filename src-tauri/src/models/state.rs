use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub enum AppState {
    Initial,
    ValidatingCookie,
    WaitingForLogin,
    Monitoring,
    <PERSON><PERSON><PERSON>pired,
    Error(String),
}

impl Default for AppState {
    fn default() -> Self {
        AppState::Initial
    }
}

impl AppState {
    pub fn to_string(&self) -> String {
        match self {
            AppState::Initial => "初始状态".to_string(),
            AppState::ValidatingCookie => "验证Cookie中".to_string(),
            AppState::WaitingForLogin => "等待登录".to_string(),
            AppState::Monitoring => "监控中".to_string(),
            AppState::CookieExpired => "Cookie已失效".to_string(),
            AppState::Error(msg) => format!("错误: {}", msg),
        }
    }

    pub fn is_running(&self) -> bool {
        matches!(self, AppState::Monitoring)
    }

    pub fn is_error(&self) -> bool {
        matches!(self, AppState::Error(_))
    }
}

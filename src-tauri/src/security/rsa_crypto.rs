use crate::services::LogService;
use aes_gcm::{
    aead::{<PERSON>ead, AeadCore, KeyInit, OsRng},
    Aes256Gcm, Key, Nonce,
};
use base64::{engine::general_purpose, Engine as _};
use rand::rngs::OsRng as RsaOsRng;
use rsa::{
    pkcs1::DecodeRsaPublicKey,
    pkcs8::{DecodePrivateKey, DecodePublicKey},
    Pkcs1v15Encrypt, RsaPrivateKey, RsaPublicKey,
};
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use std::fs;
use std::path::PathBuf;

/// 加密的激活码数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptedActivationData {
    pub device_fingerprint: String,
    pub user_id: String,
    pub expire_timestamp: i64,
    pub issued_timestamp: i64,
    pub features: Vec<String>,
}

/// 签名的激活数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SignedActivationData {
    pub data: String,      // JSON格式的激活数据
    pub signature: String, // Base64编码的RSA签名
}

/// RSA加密服务
#[derive(Clone)]
pub struct RsaCryptoService {
    log_service: LogService,
}

impl RsaCryptoService {
    pub fn new(log_service: LogService) -> Self {
        Self { log_service }
    }

    /// 获取公钥文件路径
    fn get_public_key_path(&self) -> PathBuf {
        // 优先从环境变量获取
        if let Ok(path) = std::env::var("GOLDFISH_PUBLIC_KEY_PATH") {
            return PathBuf::from(path);
        }

        // 开发环境：从项目根目录的 src-tauri/keys/ 读取
        if cfg!(debug_assertions) {
            // 使用 CARGO_MANIFEST_DIR 确保路径正确
            let manifest_dir =
                std::env::var("CARGO_MANIFEST_DIR").unwrap_or_else(|_| ".".to_string());
            let mut path = PathBuf::from(manifest_dir);
            path.push("keys");
            path.push("public_key.pem");
            return path;
        }

        // 生产环境：从应用资源目录读取（打包后的位置）
        let mut path = std::env::current_exe().unwrap_or_else(|_| PathBuf::from("."));
        path.pop(); // 移除可执行文件名
        path.push("keys");
        path.push("public_key.pem");
        path
    }

    /// 获取私钥文件路径（仅用于激活码生成，不打包到应用中）
    fn get_private_key_path(&self) -> PathBuf {
        // 优先从环境变量获取
        if let Ok(path) = std::env::var("GOLDFISH_PRIVATE_KEY_PATH") {
            return PathBuf::from(path);
        }

        // 私钥始终从 examples/activation_generator 目录读取（不会打包）
        // 使用 CARGO_MANIFEST_DIR 确保路径正确
        let manifest_dir = std::env::var("CARGO_MANIFEST_DIR").unwrap_or_else(|_| ".".to_string());
        let mut path = PathBuf::from(manifest_dir);
        path.pop(); // 回到项目根目录
        path.push("examples");
        path.push("activation_generator");
        path.push("private_key.pem");
        path
    }

    /// 读取RSA公钥
    async fn load_rsa_public_key(&self) -> Result<String, String> {
        let public_key_path = self.get_public_key_path();

        match fs::read_to_string(&public_key_path) {
            Ok(content) => {
                self.log_service
                    .info("RsaCryptoService", "load_rsa_public_key", "RSA公钥加载成功")
                    .await?;
                Ok(content.trim().to_string())
            }
            Err(e) => {
                let error_msg = format!("读取RSA公钥文件失败: {} (路径: {:?})", e, public_key_path);
                self.log_service
                    .error("RsaCryptoService", "load_rsa_public_key", &error_msg)
                    .await?;
                Err(error_msg)
            }
        }
    }

    /// 读取RSA私钥
    async fn load_rsa_private_key(&self) -> Result<String, String> {
        let private_key_path = self.get_private_key_path();

        match fs::read_to_string(&private_key_path) {
            Ok(content) => {
                self.log_service
                    .info(
                        "RsaCryptoService",
                        "load_rsa_private_key",
                        "RSA私钥加载成功",
                    )
                    .await?;
                Ok(content.trim().to_string())
            }
            Err(e) => {
                let error_msg =
                    format!("读取RSA私钥文件失败: {} (路径: {:?})", e, private_key_path);
                self.log_service
                    .error("RsaCryptoService", "load_rsa_private_key", &error_msg)
                    .await?;
                Err(error_msg)
            }
        }
    }

    /// 生成公钥指纹
    pub async fn generate_public_key_fingerprint(
        &self,
        public_key_pem: &str,
    ) -> Result<String, String> {
        let mut hasher = Sha256::new();
        hasher.update(public_key_pem.as_bytes());
        let hash = hasher.finalize();
        Ok(hex::encode(hash)) // 使用完整的SHA-256哈希作为指纹
    }

    /// 使用RSA公钥加密激活码数据
    pub async fn encrypt_activation_data(
        &self,
        data: &EncryptedActivationData,
        public_key_pem: &str,
    ) -> Result<String, String> {
        self.log_service
            .debug(
                "RsaCryptoService",
                "encrypt_activation_data",
                "开始RSA加密激活码数据",
            )
            .await?;

        // 序列化数据
        let json_data =
            serde_json::to_string(data).map_err(|e| format!("序列化激活码数据失败: {}", e))?;

        // 由于RSA加密有长度限制，我们使用混合加密：
        // 1. 生成随机AES密钥
        // 2. 用AES加密数据
        // 3. 用RSA加密AES密钥
        let aes_key = Aes256Gcm::generate_key(OsRng);
        let cipher = Aes256Gcm::new(&aes_key);
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);

        // AES加密数据
        let encrypted_data = cipher
            .encrypt(&nonce, json_data.as_bytes())
            .map_err(|e| format!("AES加密失败: {}", e))?;

        // RSA加密AES密钥
        let public_key = RsaPublicKey::from_pkcs1_pem(public_key_pem)
            .map_err(|e| format!("解析RSA公钥失败: {}", e))?;

        let mut rng = RsaOsRng;
        let encrypted_aes_key = public_key
            .encrypt(&mut rng, Pkcs1v15Encrypt, aes_key.as_slice())
            .map_err(|e| format!("RSA加密AES密钥失败: {}", e))?;

        // 组合结果：RSA加密的AES密钥 + Nonce + AES加密的数据
        let mut result = Vec::new();
        result.extend_from_slice(&(encrypted_aes_key.len() as u32).to_be_bytes());
        result.extend_from_slice(&encrypted_aes_key);
        result.extend_from_slice(&nonce);
        result.extend_from_slice(&encrypted_data);

        let encoded_result = general_purpose::STANDARD.encode(result);

        self.log_service
            .info(
                "RsaCryptoService",
                "encrypt_activation_data",
                "RSA混合加密完成",
            )
            .await?;

        Ok(encoded_result)
    }

    /// 使用RSA私钥解密激活码数据
    pub async fn decrypt_activation_data(
        &self,
        encrypted_data: &str,
        private_key_pem: &str,
    ) -> Result<EncryptedActivationData, String> {
        self.log_service
            .debug(
                "RsaCryptoService",
                "decrypt_activation_data",
                "开始RSA解密激活码数据",
            )
            .await?;

        // Base64解码
        let encrypted_bytes = general_purpose::STANDARD
            .decode(encrypted_data)
            .map_err(|e| format!("Base64解码失败: {}", e))?;

        // 解析数据结构
        if encrypted_bytes.len() < 4 {
            return Err("加密数据格式错误".to_string());
        }

        let aes_key_len = u32::from_be_bytes([
            encrypted_bytes[0],
            encrypted_bytes[1],
            encrypted_bytes[2],
            encrypted_bytes[3],
        ]) as usize;

        if encrypted_bytes.len() < 4 + aes_key_len + 12 {
            return Err("加密数据长度不足".to_string());
        }

        let encrypted_aes_key = &encrypted_bytes[4..4 + aes_key_len];
        let nonce_bytes = &encrypted_bytes[4 + aes_key_len..4 + aes_key_len + 12];
        let encrypted_payload = &encrypted_bytes[4 + aes_key_len + 12..];

        // RSA解密AES密钥
        let private_key = RsaPrivateKey::from_pkcs8_pem(private_key_pem)
            .map_err(|e| format!("解析RSA私钥失败: {}", e))?;

        let aes_key_bytes = private_key
            .decrypt(Pkcs1v15Encrypt, encrypted_aes_key)
            .map_err(|e| format!("RSA解密AES密钥失败: {}", e))?;

        let aes_key = Key::<Aes256Gcm>::from_slice(&aes_key_bytes);
        let cipher = Aes256Gcm::new(aes_key);
        let nonce = Nonce::from_slice(nonce_bytes);

        // AES解密数据
        let decrypted_data = cipher
            .decrypt(nonce, encrypted_payload)
            .map_err(|e| format!("AES解密失败: {}", e))?;

        let json_str =
            String::from_utf8(decrypted_data).map_err(|e| format!("UTF-8转换失败: {}", e))?;

        let activation_data: EncryptedActivationData = serde_json::from_str(&json_str)
            .map_err(|e| format!("反序列化激活码数据失败: {}", e))?;

        self.log_service
            .info(
                "RsaCryptoService",
                "decrypt_activation_data",
                "RSA混合解密完成",
            )
            .await?;

        Ok(activation_data)
    }

    /// 生成数字签名
    pub async fn sign_data(&self, data: &str, private_key_pem: &str) -> Result<String, String> {
        use rsa::pss::{BlindedSigningKey, Signature};
        use rsa::signature::{RandomizedSigner, SignatureEncoding};
        use sha2::Sha256;

        let private_key = RsaPrivateKey::from_pkcs8_pem(private_key_pem)
            .map_err(|e| format!("解析RSA私钥失败: {}", e))?;

        // 使用PSS签名方案（更安全）
        let signing_key = BlindedSigningKey::<Sha256>::new(private_key);
        let mut rng = rand::rngs::OsRng;

        let signature: Signature = signing_key.sign_with_rng(&mut rng, data.as_bytes());

        Ok(general_purpose::STANDARD.encode(signature.to_bytes()))
    }

    /// 验证数字签名
    pub async fn verify_signature(
        &self,
        data: &str,
        signature: &str,
        public_key_pem: &str,
    ) -> Result<bool, String> {
        use rsa::pss::{Signature, VerifyingKey};
        use rsa::signature::Verifier;
        use sha2::Sha256;

        // 尝试不同的公钥格式
        let public_key = if public_key_pem.contains("BEGIN RSA PUBLIC KEY") {
            // PKCS#1格式
            RsaPublicKey::from_pkcs1_pem(public_key_pem)
                .map_err(|e| format!("解析PKCS#1公钥失败: {}", e))?
        } else {
            // PKCS#8格式
            RsaPublicKey::from_public_key_pem(public_key_pem)
                .map_err(|e| format!("解析PKCS#8公钥失败: {}", e))?
        };

        // 解码签名
        let signature_bytes = general_purpose::STANDARD
            .decode(signature)
            .map_err(|e| format!("签名Base64解码失败: {}", e))?;

        // 使用PSS验证方案
        let verifying_key = VerifyingKey::<Sha256>::new(public_key);
        let signature = Signature::try_from(signature_bytes.as_slice())
            .map_err(|e| format!("签名格式错误: {}", e))?;

        // 验证签名
        match verifying_key.verify(data.as_bytes(), &signature) {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }

    /// 生成安全的激活码（使用RSA加密）
    pub async fn generate_secure_activation_code(
        &self,
        device_fingerprint: &str,
        user_id: &str,
        expire_minutes: i64,
        features: Vec<String>,
        public_key_pem: &str,
    ) -> Result<String, String> {
        let now = chrono::Utc::now();
        let expire_time = now + chrono::Duration::minutes(expire_minutes);

        let activation_data = EncryptedActivationData {
            device_fingerprint: device_fingerprint.to_string(),
            user_id: user_id.to_string(),
            expire_timestamp: expire_time.timestamp(),
            issued_timestamp: now.timestamp(),
            features,
        };

        self.encrypt_activation_data(&activation_data, public_key_pem)
            .await
    }

    /// 验证安全的激活码（使用RSA解密）
    pub async fn validate_secure_activation_code(
        &self,
        activation_code: &str,
        private_key_pem: &str,
        current_device_fingerprint: &str,
    ) -> Result<EncryptedActivationData, String> {
        let activation_data = self
            .decrypt_activation_data(activation_code, private_key_pem)
            .await?;

        // 验证设备指纹
        if activation_data.device_fingerprint != current_device_fingerprint {
            return Err("激活码与当前设备不匹配".to_string());
        }

        // 验证过期时间
        let now = chrono::Utc::now();
        let expire_time = chrono::DateTime::from_timestamp(activation_data.expire_timestamp, 0)
            .ok_or("无效的过期时间戳")?;

        if now > expire_time {
            return Err("激活码已过期".to_string());
        }

        // 验证签发时间（防止时间篡改）
        let issued_time = chrono::DateTime::from_timestamp(activation_data.issued_timestamp, 0)
            .ok_or("无效的签发时间戳")?;

        if issued_time > now {
            return Err("激活码签发时间异常".to_string());
        }

        Ok(activation_data)
    }

    /// 使用公钥验证激活码的数字签名
    pub async fn validate_secure_activation_code_with_public_key(
        &self,
        activation_code: &str,
        public_key_pem: &str,
        current_device_fingerprint: &str,
    ) -> Result<EncryptedActivationData, String> {
        self.log_service
            .info(
                "RsaCryptoService",
                "validate_secure_activation_code_with_public_key",
                "开始使用公钥验证激活码",
            )
            .await?;

        // 解码Base64激活码
        let decoded_data = general_purpose::STANDARD
            .decode(activation_code)
            .map_err(|e| format!("激活码Base64解码失败: {}", e))?;
        let json_string =
            String::from_utf8(decoded_data).map_err(|e| format!("激活码UTF-8解码失败: {}", e))?;

        self.log_service
            .debug(
                "RsaCryptoService",
                "validate_secure_activation_code_with_public_key",
                &format!("解码后的JSON: {}", json_string),
            )
            .await
            .ok();

        // 解析签名数据结构
        let signed_data: SignedActivationData =
            serde_json::from_str(&json_string).map_err(|e| format!("签名数据解析失败: {}", e))?;

        self.log_service
            .debug(
                "RsaCryptoService",
                "validate_secure_activation_code_with_public_key",
                &format!("提取的数据: {}", signed_data.data),
            )
            .await
            .ok();
        self.log_service
            .debug(
                "RsaCryptoService",
                "validate_secure_activation_code_with_public_key",
                &format!("提取的签名: {}", signed_data.signature),
            )
            .await
            .ok();

        // 验证签名
        let is_valid = self
            .verify_signature(&signed_data.data, &signed_data.signature, public_key_pem)
            .await?;
        if !is_valid {
            return Err("激活码签名验证失败".to_string());
        }

        self.log_service
            .info(
                "RsaCryptoService",
                "validate_secure_activation_code_with_public_key",
                "签名验证成功",
            )
            .await?;

        // 解析激活数据
        let activation_data: EncryptedActivationData = serde_json::from_str(&signed_data.data)
            .map_err(|e| format!("激活数据解析失败: {}", e))?;

        self.log_service
            .debug(
                "RsaCryptoService",
                "validate_secure_activation_code_with_public_key",
                &format!("激活数据: {:?}", activation_data),
            )
            .await
            .ok();
        self.log_service
            .debug(
                "RsaCryptoService",
                "validate_secure_activation_code_with_public_key",
                &format!("当前设备指纹: {}", current_device_fingerprint),
            )
            .await
            .ok();

        // 验证设备指纹
        if activation_data.device_fingerprint != current_device_fingerprint {
            return Err("激活码与当前设备不匹配".to_string());
        }

        // 验证时间戳
        let now = chrono::Utc::now().timestamp();

        if activation_data.expire_timestamp <= now {
            return Err("激活码已过期".to_string());
        }

        // 验证签发时间（防止时间篡改）
        let issued_time = chrono::DateTime::from_timestamp(activation_data.issued_timestamp, 0)
            .ok_or("无效的签发时间戳")?;

        if issued_time > chrono::Utc::now() {
            return Err("激活码签发时间异常".to_string());
        }

        self.log_service
            .info(
                "RsaCryptoService",
                "validate_secure_activation_code_with_public_key",
                "公钥验证激活码成功",
            )
            .await?;

        Ok(activation_data)
    }

    /// 生成带数字签名的激活码（服务端使用）
    pub async fn generate_signed_activation_code(
        &self,
        device_fingerprint: &str,
        user_id: &str,
        expire_minutes: i64,
        features: Vec<String>,
        private_key_pem: &str,
    ) -> Result<String, String> {
        self.log_service
            .info(
                "RsaCryptoService",
                "generate_signed_activation_code",
                &format!("生成带签名的激活码 - 用户: {}", user_id),
            )
            .await?;

        let now = chrono::Utc::now();
        let expire_time = now + chrono::Duration::minutes(expire_minutes);

        let activation_data = EncryptedActivationData {
            device_fingerprint: device_fingerprint.to_string(),
            user_id: user_id.to_string(),
            expire_timestamp: expire_time.timestamp(),
            issued_timestamp: now.timestamp(),
            features,
        };

        // 序列化激活数据为JSON字符串
        let json_data = serde_json::to_string(&activation_data)
            .map_err(|e| format!("激活数据序列化失败: {}", e))?;

        self.log_service
            .debug(
                "RsaCryptoService",
                "generate_signed_activation_code",
                &format!("激活数据JSON: {}", json_data),
            )
            .await
            .ok();

        // 使用私钥对JSON字符串进行签名
        let signature = self.sign_data(&json_data, private_key_pem).await?;

        self.log_service
            .debug(
                "RsaCryptoService",
                "generate_signed_activation_code",
                &format!("生成签名: {}", signature),
            )
            .await
            .ok();

        // 创建包含数据和签名的结构
        let signed_data = SignedActivationData {
            data: json_data,
            signature,
        };

        // 序列化整个签名数据结构
        let final_json = serde_json::to_string(&signed_data)
            .map_err(|e| format!("序列化签名数据失败: {}", e))?;

        // Base64编码最终结果
        let activation_code = general_purpose::STANDARD.encode(final_json);

        self.log_service
            .info(
                "RsaCryptoService",
                "generate_signed_activation_code",
                "带签名的激活码生成成功",
            )
            .await?;

        Ok(activation_code)
    }
}

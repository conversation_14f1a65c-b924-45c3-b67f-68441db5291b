// RSA加密模块
use rsa::{RsaPrivate<PERSON>ey, RsaPublic<PERSON>ey};
use anyhow::Result;

/// RSA密钥对结构
pub struct RsaKeyPair {
    pub private_key: RsaPrivateKey,
    pub public_key: RsaPublicKey,
}

/// 生成RSA密钥对
pub fn generate_rsa_keypair() -> Result<RsaKeyPair> {
    let mut rng = rand::thread_rng();
    let private_key = RsaPrivateKey::new(&mut rng, 2048)?;
    let public_key = RsaPublicKey::from(&private_key);
    
    Ok(RsaKeyPair {
        private_key,
        public_key,
    })
}

/// RSA加密
pub fn rsa_encrypt(data: &[u8], public_key: &RsaPublicKey) -> Result<Vec<u8>> {
    use rsa::Pkcs1v15Encrypt;
    let mut rng = rand::thread_rng();
    let encrypted = public_key.encrypt(&mut rng, Pkcs1v15Encrypt, data)?;
    Ok(encrypted)
}

/// RSA解密
pub fn rsa_decrypt(encrypted_data: &[u8], private_key: &RsaPrivateKey) -> Result<Vec<u8>> {
    use rsa::Pkcs1v15Encrypt;
    let decrypted = private_key.decrypt(Pkcs1v15Encrypt, encrypted_data)?;
    Ok(decrypted)
}

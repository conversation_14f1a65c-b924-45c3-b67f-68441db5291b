// 闲鱼API加密算法模块
// 基于老版本代码实现MD5签名算法

use md5;

/// 应用程序密钥
pub const APP_KEY: u32 = 34839810;

/// JavaScript版本
pub const JSV: &str = "2.7.2";

/// 计算MD5哈希并返回十六进制字符串
///
/// # Arguments
///
/// * `input` - 要哈希的字符串
///
/// # Returns
///
/// * MD5哈希的十六进制字符串表示
pub fn get_sign(input: &str) -> String {
    // 使用md5 0.7版本的API直接计算哈希
    let digest = md5::compute(input.as_bytes());

    // 转换为小写十六进制字符串
    format!("{:x}", digest)
}

/// 构建签名输入字符串并计算MD5哈希
///
/// # Arguments
///
/// * `token` - 认证令牌
/// * `timestamp` - 当前时间戳
/// * `data` - JSON数据字符串
///
/// # Returns
///
/// * 构建字符串的MD5哈希
pub fn sign_request(token: &str, timestamp: u64, data: &str) -> String {
    // 按格式构建输入字符串: token&timestamp&app_key&data
    let input = format!("{}&{}&{}&{}", token, timestamp, APP_KEY, data);

    // 计算MD5哈希
    get_sign(&input)
}

/// 默认配置结构
pub struct Config {
    pub data: String,
    pub token: String,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            data: r#"{"pageNumber":6,"keyword":"相机","fromFilter":false,"rowsPerPage":30,"sortValue":"","sortField":"","customDistance":"","gps":"","propValueStr":{},"customGps":"","searchReqFromPage":"pcSearch","extraFilterValue":"{}","userPositionJson":"{}"}"#.to_string(),
            token: String::new(), // 空令牌，将设置_m_h5_tk和_m_h5_tk_enc
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_md5_hash() {
        // 使用已知输入/输出对进行测试
        assert_eq!(get_sign("test"), "098f6bcd4621d373cade4e832627b4f6");
    }

    #[test]
    fn test_sign_request() {
        let token = "sample_token";
        let timestamp = 1600000000000;
        let data = r#"{"test":"data"}"#;

        let expected = get_sign(&format!("{}&{}&{}&{}", token, timestamp, APP_KEY, data));
        let result = sign_request(token, timestamp, data);

        assert_eq!(result, expected);
    }

    #[test]
    fn test_constants() {
        assert_eq!(APP_KEY, 34839810);
        assert_eq!(JSV, "2.7.2");
    }
}

use crate::utils::{WindowFactory, WindowParams, WindowConfig};
use serde::{Deserialize, Serialize};
use tauri::{command, AppHandle, State, WebviewWindow};

/// 窗口创建请求参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateWindowRequest {
    /// 窗口URL（必填）
    pub url: String,
    /// 窗口标签（可选）
    pub label: Option<String>,
    /// 窗口标题（可选）
    pub title: Option<String>,
    /// 窗口配置（可选）
    pub window_config: Option<WindowConfigRequest>,
    /// 存储隔离标识（可选）
    pub identity: Option<String>,
}

/// 窗口配置请求参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowConfigRequest {
    pub width: Option<f64>,
    pub height: Option<f64>,
    pub x: Option<f64>,
    pub y: Option<f64>,
    pub resizable: Option<bool>,
    pub closable: Option<bool>,
    pub minimizable: Option<bool>,
    pub maximizable: Option<bool>,
    pub decorations: Option<bool>,
    pub always_on_top: Option<bool>,
    pub skip_taskbar: Option<bool>,
    pub fullscreen: Option<bool>,
    pub center: Option<bool>,
    pub min_width: Option<f64>,
    pub min_height: Option<f64>,
    pub max_width: Option<f64>,
    pub max_height: Option<f64>,
    pub visible: Option<bool>,
    pub focused: Option<bool>,
}

impl From<WindowConfigRequest> for WindowConfig {
    fn from(req: WindowConfigRequest) -> Self {
        WindowConfig {
            width: req.width,
            height: req.height,
            x: req.x,
            y: req.y,
            resizable: req.resizable,
            closable: req.closable,
            minimizable: req.minimizable,
            maximizable: req.maximizable,
            decorations: req.decorations,
            always_on_top: req.always_on_top,
            skip_taskbar: req.skip_taskbar,
            fullscreen: req.fullscreen,
            center: req.center,
            min_width: req.min_width,
            min_height: req.min_height,
            max_width: req.max_width,
            max_height: req.max_height,
            visible: req.visible,
            focused: req.focused,
        }
    }
}

/// 窗口创建响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateWindowResponse {
    pub label: String,
    pub success: bool,
    pub message: String,
}

/// 使用窗口工厂创建窗口
#[command]
pub async fn window_factory_create(
    app: AppHandle,
    request: CreateWindowRequest,
) -> Result<CreateWindowResponse, String> {
    // 构建窗口参数
    let mut params = WindowParams::new();
    
    if let Some(title) = request.title {
        params = params.with_title(title);
    }
    
    if let Some(config_req) = request.window_config {
        let config: WindowConfig = config_req.into();
        params = params.with_config(config);
    }

    // 创建窗口
    let window = if let Some(label) = request.label {
        WindowFactory::create_window_with_label(
            &app,
            &label,
            &request.url,
            Some(params),
            request.identity.as_deref(),
        )?
    } else {
        WindowFactory::create_window(
            &app,
            &request.url,
            Some(params),
            request.identity.as_deref(),
        )?
    };

    Ok(CreateWindowResponse {
        label: window.label().to_string(),
        success: true,
        message: "窗口创建成功".to_string(),
    })
}

/// 创建简单浏览器窗口
#[command]
pub async fn window_factory_create_simple_browser(
    app: AppHandle,
    url: String,
    title: Option<String>,
) -> Result<CreateWindowResponse, String> {
    let mut params = WindowParams::new();
    
    if let Some(title) = title {
        params = params.with_title(title);
    }

    let window = WindowFactory::create_window(&app, &url, Some(params), None)?;

    Ok(CreateWindowResponse {
        label: window.label().to_string(),
        success: true,
        message: "简单浏览器窗口创建成功".to_string(),
    })
}

/// 创建小窗口
#[command]
pub async fn window_factory_create_small_window(
    app: AppHandle,
    url: String,
    title: String,
) -> Result<CreateWindowResponse, String> {
    let params = WindowParams::new()
        .with_title(title)
        .with_config(WindowConfig::small());

    let window = WindowFactory::create_window(&app, &url, Some(params), None)?;

    Ok(CreateWindowResponse {
        label: window.label().to_string(),
        success: true,
        message: "小窗口创建成功".to_string(),
    })
}

/// 创建大窗口
#[command]
pub async fn window_factory_create_large_window(
    app: AppHandle,
    url: String,
    title: String,
) -> Result<CreateWindowResponse, String> {
    let params = WindowParams::new()
        .with_title(title)
        .with_config(WindowConfig::large());

    let window = WindowFactory::create_window(&app, &url, Some(params), None)?;

    Ok(CreateWindowResponse {
        label: window.label().to_string(),
        success: true,
        message: "大窗口创建成功".to_string(),
    })
}

/// 创建全屏窗口
#[command]
pub async fn window_factory_create_fullscreen_window(
    app: AppHandle,
    url: String,
    title: String,
) -> Result<CreateWindowResponse, String> {
    let params = WindowParams::new()
        .with_title(title)
        .with_config(WindowConfig::fullscreen());

    let window = WindowFactory::create_window(&app, &url, Some(params), None)?;

    Ok(CreateWindowResponse {
        label: window.label().to_string(),
        success: true,
        message: "全屏窗口创建成功".to_string(),
    })
}

/// 创建带存储隔离的窗口
#[command]
pub async fn window_factory_create_isolated_window(
    app: AppHandle,
    url: String,
    title: String,
    identity: String,
) -> Result<CreateWindowResponse, String> {
    let params = WindowParams::new().with_title(title);

    let window = WindowFactory::create_window(&app, &url, Some(params), Some(&identity))?;

    Ok(CreateWindowResponse {
        label: window.label().to_string(),
        success: true,
        message: "隔离窗口创建成功".to_string(),
    })
}

/// 创建自定义尺寸窗口
#[command]
pub async fn window_factory_create_custom_size_window(
    app: AppHandle,
    url: String,
    title: String,
    width: f64,
    height: f64,
) -> Result<CreateWindowResponse, String> {
    let config = WindowConfig::default().with_size(width, height);
    let params = WindowParams::new()
        .with_title(title)
        .with_config(config);

    let window = WindowFactory::create_window(&app, &url, Some(params), None)?;

    Ok(CreateWindowResponse {
        label: window.label().to_string(),
        success: true,
        message: "自定义尺寸窗口创建成功".to_string(),
    })
}

/// 创建置顶窗口
#[command]
pub async fn window_factory_create_always_on_top_window(
    app: AppHandle,
    url: String,
    title: String,
) -> Result<CreateWindowResponse, String> {
    let mut config = WindowConfig::small();
    config.always_on_top = Some(true);
    
    let params = WindowParams::new()
        .with_title(title)
        .with_config(config);

    let window = WindowFactory::create_window(&app, &url, Some(params), None)?;

    Ok(CreateWindowResponse {
        label: window.label().to_string(),
        success: true,
        message: "置顶窗口创建成功".to_string(),
    })
}

/// 创建无装饰窗口
#[command]
pub async fn window_factory_create_borderless_window(
    app: AppHandle,
    url: String,
    title: String,
) -> Result<CreateWindowResponse, String> {
    let mut config = WindowConfig::default().with_size(800.0, 600.0);
    config.decorations = Some(false);
    config.resizable = Some(false);
    
    let params = WindowParams::new()
        .with_title(title)
        .with_config(config);

    let window = WindowFactory::create_window(&app, &url, Some(params), None)?;

    Ok(CreateWindowResponse {
        label: window.label().to_string(),
        success: true,
        message: "无装饰窗口创建成功".to_string(),
    })
}

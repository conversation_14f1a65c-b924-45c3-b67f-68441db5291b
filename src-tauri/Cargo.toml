[package]
name = "goldfish-app"
version = "0.1.0"
description = "闲鱼商品监控工具"
authors = ["goldfish-app"]
license = ""
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
# Tauri 核心依赖
tauri = { version = "2.0", features = ["devtools"] }
tauri-plugin-opener = "2.0"
tauri-plugin-log = "2.0"

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 异步运行时
tokio = { version = "1.0", features = ["full"] }
tokio-util = "0.7"
once_cell = "1.19"

# 加密相关
aes = "0.8"
aes-gcm = "0.10"
md5 = "0.7"
sha2 = "0.10"
rsa = "0.9"
hex = "0.4"
base64 = "0.22"

# 网络相关
reqwest = { version = "0.12", features = ["json", "cookies"] }
url = "2.5"

# 系统信息
sysinfo = "0.31"
hostname = "0.4"

# 工具库
anyhow = "1.0"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
rand = "0.8"

# 日志
log = "0.4"

# 其他工具
regex = "1.11.1"

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

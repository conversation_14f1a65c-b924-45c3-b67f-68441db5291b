# 使用Ubuntu作为基础镜像进行Windows交叉编译
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV RUST_VERSION=1.75.0
ENV NODE_VERSION=18

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    build-essential \
    pkg-config \
    libssl-dev \
    libgtk-3-dev \
    libwebkit2gtk-4.0-dev \
    libappindicator3-dev \
    librsvg2-dev \
    mingw-w64 \
    gcc-mingw-w64 \
    g++-mingw-w64 \
    cmake \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安装Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash - \
    && apt-get install -y nodejs

# 安装Rust
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --default-toolchain ${RUST_VERSION}
ENV PATH="/root/.cargo/bin:${PATH}"

# 添加Windows目标
RUN rustup target add x86_64-pc-windows-gnu

# 设置交叉编译环境
ENV CC_x86_64_pc_windows_gnu=x86_64-w64-mingw32-gcc
ENV CXX_x86_64_pc_windows_gnu=x86_64-w64-mingw32-g++
ENV AR_x86_64_pc_windows_gnu=x86_64-w64-mingw32-ar
ENV CARGO_TARGET_X86_64_PC_WINDOWS_GNU_LINKER=x86_64-w64-mingw32-gcc

# 安装Tauri CLI
RUN npm install -g @tauri-apps/cli

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY package*.json ./
RUN npm install

COPY . .

# 构建脚本
RUN echo '#!/bin/bash\n\
npm run build\n\
npm run tauri build -- --target x86_64-pc-windows-gnu\n\
' > /build.sh && chmod +x /build.sh

CMD ["/build.sh"]

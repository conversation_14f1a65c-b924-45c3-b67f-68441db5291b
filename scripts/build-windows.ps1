# Windows打包脚本
# 在Windows系统上运行此脚本来构建应用

Write-Host "开始Windows平台打包..." -ForegroundColor Green

# 检查Node.js
if (!(Get-Command "node" -ErrorAction SilentlyContinue)) {
    Write-Host "错误: 未找到Node.js，请先安装Node.js" -ForegroundColor Red
    exit 1
}

# 检查Rust
if (!(Get-Command "cargo" -ErrorAction SilentlyContinue)) {
    Write-Host "错误: 未找到Rust，请先安装Rust" -ForegroundColor Red
    exit 1
}

# 检查Tauri CLI
Write-Host "检查Tauri CLI..." -ForegroundColor Yellow
npm list -g @tauri-apps/cli
if ($LASTEXITCODE -ne 0) {
    Write-Host "安装Tauri CLI..." -ForegroundColor Yellow
    npm install -g @tauri-apps/cli
}

# 安装依赖
Write-Host "安装依赖..." -ForegroundColor Yellow
npm install

# 构建前端
Write-Host "构建前端..." -ForegroundColor Yellow
npm run build

# 构建Tauri应用
Write-Host "构建Tauri应用..." -ForegroundColor Yellow
npm run tauri build

if ($LASTEXITCODE -eq 0) {
    Write-Host "打包完成！" -ForegroundColor Green
    Write-Host "输出文件位置:" -ForegroundColor Cyan
    Write-Host "  MSI安装包: src-tauri/target/release/bundle/msi/" -ForegroundColor Cyan
    Write-Host "  NSIS安装包: src-tauri/target/release/bundle/nsis/" -ForegroundColor Cyan
    Write-Host "  可执行文件: src-tauri/target/release/" -ForegroundColor Cyan
} else {
    Write-Host "打包失败！" -ForegroundColor Red
    exit 1
}

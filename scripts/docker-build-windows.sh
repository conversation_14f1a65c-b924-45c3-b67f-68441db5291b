#!/bin/bash

# Docker Windows交叉编译脚本

echo "🚀 开始使用Docker进行Windows交叉编译..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: 未找到Docker，请先安装Docker"
    exit 1
fi

# 构建Docker镜像
echo "📦 构建Docker镜像..."
docker build -f Dockerfile.windows -t goldfish-windows-builder .

if [ $? -ne 0 ]; then
    echo "❌ Docker镜像构建失败"
    exit 1
fi

# 创建输出目录
mkdir -p ./dist/windows

# 运行Docker容器进行编译
echo "🔨 开始编译..."
docker run --rm \
    -v "$(pwd)/dist/windows:/app/src-tauri/target/x86_64-pc-windows-gnu/release" \
    goldfish-windows-builder

if [ $? -eq 0 ]; then
    echo "✅ Windows编译完成！"
    echo "📁 输出文件位置: ./dist/windows/"
    ls -la ./dist/windows/
else
    echo "❌ 编译失败"
    exit 1
fi

# ===== Node.js 相关 =====
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
dist-ssr/
*.local

# ===== Rust/Tauri 相关 =====
# Rust build artifacts
src-tauri/target/
src-tauri/Cargo.lock

# Tauri build outputs
src-tauri/WixTools/
src-tauri/wix/
src-tauri/bundle/

# ===== 系统文件 =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== IDE/编辑器 =====
# VSCode
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# Sublime Text
*.sublime-workspace
*.sublime-project

# Cursor IDE
.cursor/

# ===== Python 相关 =====
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ===== 应用特定文件 =====
# 配置文件（包含敏感信息）
config.json
certs/monitor_config.json

# 日志文件
logs/
*.log
goldfish.log
error.log
access.log

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 构建产物
*.dmg
*.pkg
*.deb
*.rpm
*.exe
*.msi
*.appx
*.AppImage
goldfish-app-*.dmg
goldfish-app-*.exe
goldfish-app-*.deb
goldfish-app-*.rpm
goldfish-app-*.pkg
goldfish-app.app/

# ===== 临时文件 =====
# 任务进度文件
task_progress.md

# 测试覆盖率
coverage/
*.lcov

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===== 证书和密钥 =====
# SSL证书
*.pem
*.key
*.cer
*.der
*.p7b
*.p7c
*.p12
*.pfx
*.jks
!src-tauri/keys/public_key.pem
# SSH密钥
id_rsa
id_dsa
id_ecdsa
id_ed25519
*.pub

# ===== 其他 =====
# 备份文件
*.bak
*.backup
*.old
*.orig

# 压缩文件
*.zip
*.tar.gz
*.tgz
*.rar
*.7z

# 内存转储文件
*.dmp
*.stackdump

# 文档草稿
*.draft
*.wip

# 本地开发标记文件
.local
.development
.debug

# Package管理器锁文件（可选，根据团队决定）
# package-lock.json
# yarn.lock

# 公钥指纹（如果包含敏感信息）
public_key_fingerprint.txt

# Augment AI相关
.augment/
.cunzhi-memory/
.github/
accounts.json

# 🔑 Goldfish 密钥管理完整指南

## 📋 概述

Goldfish 应用使用 RSA 密钥对来生成和验证激活码。**现在支持动态读取密钥文件**，无需修改代码！

## ✅ 动态密钥读取（推荐）

**好消息！** 应用现在支持动态读取密钥文件：

### 📁 密钥文件位置
- **公钥**：`examples/activation_generator/public_key.pem`
- **私钥**：`examples/activation_generator/private_key.pem`

### 🔄 重新生成密钥的简单步骤
1. 运行 `python activation_generator.py`（设置 `generate_keys = True`）
2. 重新启动应用
3. 应用会自动读取新的密钥文件
4. **无需修改任何代码！**

### ⚠️ 重要提醒

**重新生成密钥的影响：**
- 🚫 所有现有激活码将立即失效
- 🔄 需要重新为所有用户生成激活码
- 🔧 应用需要重新启动（但无需重新编译）

## 🎯 环境变量配置（可选）

如果需要自定义密钥文件位置，可以设置环境变量：

```bash
export GOLDFISH_PUBLIC_KEY_PATH="/path/to/your/public_key.pem"
export GOLDFISH_PRIVATE_KEY_PATH="/path/to/your/private_key.pem"
```

如果不设置环境变量，应用会使用默认路径：
- 公钥：`examples/activation_generator/public_key.pem`
- 私钥：`examples/activation_generator/private_key.pem`

## 🚀 操作方法

### 方式一：使用配置模式（推荐）

直接修改配置文件重新生成密钥：

```bash
cd examples/activation_generator
# 编辑 activation_generator.py
# 设置 CONFIG['generate_keys'] = True
python activation_generator.py
```

### 方式二：使用命令行模式

#### 步骤 1：生成新密钥对

```bash
cd examples/activation_generator
# 修改配置
# CONFIG['generate_keys'] = True
python activation_generator.py
```

#### 步骤 2：获取新公钥内容

```bash
cat public_key.pem
```

#### 步骤 3：手动更新 Rust 代码

编辑 `src-tauri/src/services/auth/service.rs`：

```rust
const RSA_PUBLIC_KEY_PEM: &str = r#"-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----"#;
```

#### 步骤 4：获取新设备指纹

```bash
# 启动应用获取设备指纹
npm run tauri dev
```

#### 步骤 5：更新配置文件

编辑 `activation_generator.py` 中的 `CONFIG['device_fingerprint']`

## 🔧 验证步骤

完成密钥更新后，按以下步骤验证：

### 1. 重新编译应用

```bash
npm run tauri dev
```

### 2. 获取新设备指纹

在应用的激活页面查看设备指纹

### 3. 生成测试激活码

```bash
cd examples/activation_generator
# 更新 CONFIG 中的设备指纹
# CONFIG['generate_code'] = True
python activation_generator.py
```

### 4. 验证激活码

在应用中输入生成的激活码，确认能够成功激活

## 📁 文件结构

```
examples/activation_generator/
├── activation_generator.py    # 主要工具（包含配置）
├── regenerate_keys.py        # 一键重新生成脚本
├── sync_keys.py             # 密钥同步脚本
├── test_sync.py             # 测试同步功能
├── private_key.pem          # 私钥文件
├── public_key.pem           # 公钥文件
├── backup_YYYYMMDD_HHMMSS/  # 自动备份目录
└── README.md                # 使用说明
```

## 🛡️ 安全建议

### 密钥管理
- 🔒 **私钥保护**：私钥文件权限设为 600，仅所有者可读写
- 📦 **定期备份**：定期备份密钥文件到安全位置
- 🚫 **版本控制**：不要将私钥提交到 Git 仓库
- 🔄 **定期轮换**：建议定期更换密钥对

### 激活码管理
- ⏰ **合理过期时间**：设置适当的激活码过期时间
- 🎯 **设备绑定**：激活码与设备指纹绑定，防止滥用
- 📊 **使用记录**：记录激活码的生成和使用情况

## 🔍 故障排除

### 问题：激活码验证失败

**可能原因：**
- Rust 代码中的公钥未更新
- 设备指纹不匹配
- 激活码已过期

**解决方案：**
1. 运行 `python sync_keys.py` 同步公钥
2. 重新编译应用
3. 确认设备指纹正确
4. 重新生成激活码

### 问题：同步脚本失败

**可能原因：**
- 文件路径错误
- 权限不足
- 文件格式问题

**解决方案：**
1. 检查文件是否存在
2. 确认有写入权限
3. 手动更新 Rust 代码

### 问题：应用编译失败

**可能原因：**
- 公钥格式错误
- Rust 语法错误

**解决方案：**
1. 检查公钥格式是否正确
2. 确认 Rust 代码语法无误
3. 运行 `cargo check` 检查错误

## 📞 技术支持

如果遇到问题，请检查：
1. 📄 **日志文件**：查看应用日志了解详细错误信息
2. 🔧 **编译输出**：查看 Rust 编译错误信息
3. 📋 **配置文件**：确认所有配置项正确填写

---

**最后更新**：2025-01-17
**版本**：v1.0

# Goldfish 激活码生成器使用说明

## 🚀 快速开始

### 方法一：使用简化脚本（推荐）

1. **编辑配置**
   ```bash
   cd examples/activation_generator
   # 编辑 run_activation.py 中的 CONFIG 配置
   ```

2. **第一步：生成密钥对**
   ```python
   CONFIG = {
       'generate_keys': True,    # 设为 True
       'generate_code': False,   # 其他设为 False
       'verify_code': False,
       'show_fingerprint': False,
       # ... 其他配置
   }
   ```
   
   运行：
   ```bash
   python run_activation.py
   ```

3. **第二步：生成激活码**
   ```python
   CONFIG = {
       'generate_keys': False,   # 设为 False
       'generate_code': True,    # 设为 True
       'verify_code': False,
       'show_fingerprint': False,
       
       # 填入实际参数
       'device_fingerprint': '实际的设备指纹',  # 从应用中获取
       'user_id': 'user_001',
       'expire_days': 365,
       'features': ['monitor', 'notification'],
   }
   ```
   
   运行：
   ```bash
   python run_activation.py
   ```

### 方法二：使用原始脚本

1. **直接运行（使用内置配置）**
   ```bash
   cd examples/activation_generator
   python activation_generator.py
   ```

2. **使用命令行参数**
   ```bash
   # 生成密钥对
   python activation_generator.py --generate-keys
   
   # 生成激活码
   python activation_generator.py \
       --private-key private_key.pem \
       --device-fingerprint "设备指纹" \
       --user-id "用户ID" \
       --expire-days 365
   
   # 验证激活码
   python activation_generator.py \
       --public-key public_key.pem \
       --verify "激活码" \
       --device-fingerprint "设备指纹"
   ```

## 📋 配置说明

### 操作模式
- `generate_keys`: 生成RSA密钥对
- `generate_code`: 生成激活码
- `verify_code`: 验证激活码
- `show_fingerprint`: 显示公钥指纹

### 文件路径
- `private_key_path`: 私钥文件路径
- `public_key_path`: 公钥文件路径

### 激活码参数
- `device_fingerprint`: 设备指纹（从应用中获取）
- `user_id`: 用户ID
- `expire_days`: 过期天数
- `features`: 功能列表

## 🔧 获取设备指纹

在Goldfish应用中：
1. 启动应用
2. 进入激活页面
3. 查看显示的设备指纹
4. 复制设备指纹到配置中

## 📝 使用流程

1. **首次使用**
   - 设置 `generate_keys = True`
   - 运行脚本生成密钥对
   - 保存好生成的密钥文件

2. **生成激活码**
   - 设置 `generate_code = True`
   - 填入正确的设备指纹
   - 运行脚本获取激活码

3. **验证激活码**
   - 设置 `verify_code = True`
   - 填入要验证的激活码和设备指纹
   - 运行脚本验证

## ⚠️ 注意事项

1. **密钥安全**
   - 私钥文件需要妥善保管
   - 不要将私钥提交到版本控制系统

2. **设备指纹**
   - 每个设备的指纹都是唯一的
   - 激活码与设备指纹绑定，不能跨设备使用

3. **过期时间**
   - 激活码有过期时间限制
   - 过期后需要重新生成

## 🐛 常见问题

**Q: 提示"No such file"错误？**
A: 请先生成密钥对，设置 `generate_keys = True`

**Q: 激活码验证失败？**
A: 检查设备指纹是否正确，确保与生成时使用的指纹一致

**Q: 如何获取设备指纹？**
A: 在应用的激活页面可以看到设备指纹信息

**Q: 可以修改过期时间吗？**
A: 可以，修改 `expire_days` 参数即可

# Goldfish 激活码生成器

独立的Python工具，用于生成和验证Goldfish应用的RSA激活码。

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方式一：配置模式（推荐）

直接运行脚本，使用内置配置：

```bash
python activation_generator.py
```

在文件中修改 `CONFIG` 配置：

```python
CONFIG = {
    # 操作模式 (选择一个，其他设为False)
    'generate_keys': False,      # 生成新的密钥对
    'generate_code': True,       # 生成激活码
    'verify_code': False,        # 验证激活码
    'show_fingerprint': False,   # 显示公钥指纹

    # 激活码生成参数
    'device_fingerprint': '你的设备指纹',  # 替换为实际的设备指纹
    'user_id': 'user_001',               # 用户ID
    'expire_days': 365,                  # 过期天数
    'features': ['monitor', 'notification'],  # 功能列表
}
```

### 方式二：命令行模式

#### 1. 生成密钥对

```bash
python activation_generator.py --generate-keys --private-key private_key.pem --public-key public_key.pem
```

#### 2. 生成激活码

```bash
python activation_generator.py \
    --private-key private_key.pem \
    --device-fingerprint "设备指纹" \
    --user-id "用户ID" \
    --expire-days 365 \
    --features monitor notification
```

#### 3. 验证激活码

```bash
python activation_generator.py \
    --public-key public_key.pem \
    --verify "激活码" \
    --device-fingerprint "设备指纹"
```

#### 4. 获取公钥指纹

```bash
python activation_generator.py --public-key public_key.pem --fingerprint
```

## 🔄 密钥重新生成

### ✅ 编译时密钥读取（推荐）

**好消息！** 现在应用使用 Rust 的 `include_str!` 宏在编译时读取公钥文件！

#### 📁 密钥文件存储：
- **公钥**：`src-tauri/keys/public_key.pem` （编译时读取并嵌入到应用）
- **私钥**：`examples/activation_generator/private_key.pem` （不会打包，仅用于生成激活码）

#### 🔄 重新生成密钥的步骤：
1. 运行 `python activation_generator.py`（设置 `generate_keys = True`）
2. 重新编译应用（应用会自动读取新的公钥文件）
3. 启动应用：`npm run tauri dev`
4. **无需修改任何代码！**

#### 🛡️ 技术优势：
- **编译时读取**：使用 `include_str!` 宏，公钥在编译时嵌入到二进制文件
- **无运行时依赖**：不需要在运行时读取文件，避免文件丢失问题
- **私钥安全**：私钥永远不会打包到应用中
- **维护简便**：公钥文件可以正常编辑，编译时自动读取

### 🧪 测试动态密钥功能

使用测试脚本验证功能：

```bash
python test_dynamic_keys.py
```

这个脚本会：
1. 备份当前密钥
2. 生成新密钥对
3. 测试激活码生成和验证
4. 可选择恢复旧密钥

### ⚠️ 传统方式（不推荐）

如果你需要手动同步密钥到代码中：

### 方式一：自动重新生成（推荐）

使用一键重新生成脚本：

```bash
python regenerate_keys.py
```

这个脚本会：
1. 📦 备份旧密钥文件
2. 🔑 生成新的RSA密钥对
3. 🔄 自动同步公钥到Rust代码
4. 📝 更新配置文件示例
5. ✅ 完成所有必要的更新

### 方式二：手动同步

如果你已经重新生成了密钥，可以使用同步脚本：

```bash
python sync_keys.py
```

### 需要更新的位置

重新生成密钥后，以下位置需要更新：

1. **Rust代码中的硬编码公钥**：
   - 文件：`src-tauri/src/services/auth/service.rs`
   - 常量：`RSA_PUBLIC_KEY_PEM`

2. **配置文件中的设备指纹示例**：
   - 文件：`activation_generator.py`
   - 配置：`CONFIG['device_fingerprint']`

### 影响说明

⚠️ **重新生成密钥的影响**：
- 所有现有激活码将失效
- 需要重新为用户生成激活码
- 应用需要重新编译和部署

## 参数说明

- `--generate-keys`: 生成新的RSA密钥对
- `--private-key`: 私钥文件路径（用于生成激活码）
- `--public-key`: 公钥文件路径（用于验证激活码）
- `--device-fingerprint`: 设备指纹（从应用获取）
- `--user-id`: 用户标识
- `--expire-days`: 激活码有效期（天数）
- `--features`: 激活的功能列表
- `--verify`: 验证指定的激活码
- `--fingerprint`: 显示公钥指纹

## 工作流程

1. **管理员操作**：
   - 生成密钥对
   - 将公钥集成到应用中
   - 根据用户设备指纹生成激活码

2. **用户操作**：
   - 运行应用获取设备指纹
   - 向管理员提供设备指纹
   - 在应用中输入激活码完成激活

## 安全说明

- 私钥必须妥善保管，不能泄露
- 公钥可以集成到应用中
- 激活码与设备指纹绑定，具有唯一性
- 支持设置过期时间和功能权限

# 🔐 Goldfish 智能密钥管理系统

## 📋 概述

Goldfish 应用现在采用智能密钥管理系统，实现了**公钥私钥分离存储**，既保证了安全性，又提供了便捷的使用体验。

## 🏗️ 架构设计

### 📁 文件存储结构

```
goldfish-app-vscode/
├── src-tauri/
│   └── keys/
│       └── public_key.pem          # 🔓 公钥（会打包到应用）
├── examples/
│   └── activation_generator/
│       ├── private_key.pem         # 🔐 私钥（不会打包）
│       ├── public_key.pem          # 🔓 公钥（源文件）
│       ├── activation_generator.py # 🛠️ 主工具
│       └── sync_public_key.py      # 🔄 同步脚本
└── config.json                     # ⚙️ 应用配置
```

### 🔄 密钥读取逻辑

#### **开发环境**：
- 公钥：`src-tauri/keys/public_key.pem`
- 私钥：`examples/activation_generator/private_key.pem`

#### **生产环境**：
- 公钥：`应用目录/keys/public_key.pem`（自动打包）
- 私钥：不打包（仅在开发环境用于生成激活码）

## 🛡️ 安全优势

### ✅ **公钥打包**
- 公钥会自动打包到应用中
- 确保应用能正常验证激活码
- 用户无需额外配置

### ✅ **私钥隔离**
- 私钥永远不会打包到应用中
- 只存在于开发环境的激活码生成器目录
- 避免私钥泄露风险

### ✅ **自动同步**
- 生成新密钥时自动同步公钥到打包目录
- 无需手动复制文件
- 减少人为错误

## 🚀 使用方法

### 1. 生成新密钥对

```bash
cd examples/activation_generator
# 编辑 activation_generator.py
# 设置 CONFIG['generate_keys'] = True
python activation_generator.py
```

**自动执行的操作：**
1. 🔑 生成新的 RSA 密钥对
2. 💾 保存私钥到当前目录
3. 💾 保存公钥到当前目录
4. 🔄 自动同步公钥到 `src-tauri/keys/`
5. ✅ 完成密钥生成和同步

### 2. 手动同步公钥（可选）

如果需要手动同步公钥：

```bash
cd examples/activation_generator
python sync_public_key.py
```

### 3. 生成激活码

```bash
cd examples/activation_generator
# 编辑 activation_generator.py
# 设置 CONFIG['generate_code'] = True
# 填入正确的设备指纹
python activation_generator.py
```

### 4. 重新启动应用

```bash
npm run tauri dev
```

应用会自动读取新的公钥文件。

## 🔧 环境变量配置（高级）

如果需要自定义密钥文件位置：

```bash
# 自定义公钥路径
export GOLDFISH_PUBLIC_KEY_PATH="/custom/path/public_key.pem"

# 自定义私钥路径
export GOLDFISH_PRIVATE_KEY_PATH="/custom/path/private_key.pem"
```

## 📋 完整工作流程

### 🎯 **首次设置**

1. **生成密钥对**：
   ```bash
   cd examples/activation_generator
   # 设置 generate_keys = True
   python activation_generator.py
   ```

2. **启动应用**：
   ```bash
   npm run tauri dev
   ```

3. **获取设备指纹**：
   - 在应用中查看设备指纹
   - 复制设备指纹

4. **生成激活码**：
   ```bash
   # 设置 generate_code = True
   # 填入设备指纹
   python activation_generator.py
   ```

5. **测试激活**：
   - 在应用中输入激活码
   - 验证激活成功

### 🔄 **更新密钥**

1. **生成新密钥**：
   ```bash
   # 设置 generate_keys = True
   python activation_generator.py
   ```

2. **重启应用**：
   ```bash
   npm run tauri dev
   ```

3. **重新生成激活码**：
   - 所有旧激活码失效
   - 使用新密钥生成新激活码

## 🔍 故障排除

### ❌ **公钥读取失败**

**症状**：应用启动时提示公钥读取失败

**解决方案**：
1. 检查 `src-tauri/keys/public_key.pem` 是否存在
2. 运行同步脚本：`python sync_public_key.py`
3. 重新启动应用

### ❌ **私钥读取失败**

**症状**：生成激活码时提示私钥读取失败

**解决方案**：
1. 检查 `examples/activation_generator/private_key.pem` 是否存在
2. 重新生成密钥对
3. 确保在正确的目录中运行脚本

### ❌ **激活码验证失败**

**症状**：应用中输入激活码后验证失败

**可能原因**：
1. 公钥和私钥不匹配
2. 设备指纹不正确
3. 激活码已过期

**解决方案**：
1. 确保公钥已正确同步
2. 重新获取设备指纹
3. 生成新的激活码

## 📊 技术细节

### 🔐 **密钥格式**
- **算法**：RSA-2048
- **格式**：PEM 编码
- **签名**：PSS + SHA256

### 📁 **打包配置**
```json
// tauri.conf.json
"bundle": {
  "resources": [
    "keys/*"
  ]
}
```

### 🔄 **路径解析逻辑**
```rust
// 开发环境
if cfg!(debug_assertions) {
    path.push("src-tauri/keys/public_key.pem");
}
// 生产环境
else {
    path.push("keys/public_key.pem");
}
```

## 🎉 总结

智能密钥管理系统提供了：

- ✅ **安全性**：私钥不会打包到应用
- ✅ **便捷性**：自动同步，无需手动操作
- ✅ **灵活性**：支持环境变量自定义
- ✅ **可靠性**：智能路径解析，适应不同环境

现在你可以安全、便捷地管理 Goldfish 应用的密钥系统！🚀

# 开发模式说明

## 🔧 开发环境特性

本项目已经配置了完整的开发环境支持，可以在浏览器中进行前端开发和调试，无需Tauri环境。

### ✅ 已实现的功能

#### 1. **权限校验绕过**
- 开发环境下自动跳过路由权限校验
- 可以直接访问所有页面进行样式调试

#### 2. **Tauri API兼容**
- 自动检测Tauri环境
- 浏览器环境下使用模拟数据
- 所有Tauri API调用都有安全包装

#### 3. **模拟数据**
- 完整的配置数据模拟
- 监控数据和状态模拟
- 日志数据模拟
- 激活状态模拟

## 🚀 使用方法

### 1. **启动开发服务器**
```bash
npm run dev
# 或
yarn dev
```

### 2. **在浏览器中访问**
```
http://localhost:1420
```

### 3. **直接访问任意页面**
- 主页面: `http://localhost:1420/`
- 激活页面: `http://localhost:1420/activation`
- 加载页面: `http://localhost:1420/loading`

## 🛠️ 开发工具

### 浏览器控制台工具
在开发环境下，控制台会暴露 `window.goldfishDevTools` 对象：

```javascript
// 检查是否在Tauri环境
window.goldfishDevTools.isTauriEnv

// 查看模拟数据
window.goldfishDevTools.mockData

// 安全调用Tauri API
window.goldfishDevTools.safeInvoke('command_name', {})
```

### 模拟数据结构
```javascript
// 配置数据
mockData.config = {
  keywords: ['iPhone', 'MacBook', '显卡'],
  dingtalk_hooks: ['https://oapi.dingtalk.com/robot/send?access_token=mock'],
  login_cookie: 'mock_cookie_value',
  polling_interval: 30
}

// 监控数据
mockData.monitorData = [
  {
    title: 'iPhone 15 Pro Max 256GB',
    price: '8999',
    location: '北京',
    // ...更多字段
  }
]

// 监控状态
mockData.monitorStatus = {
  is_running: false,
  items_count: 2,
  total_found_items: 15
}
```

## 📋 开发注意事项

### 1. **环境检测**
所有与Tauri相关的代码都应该使用安全包装：

```javascript
import { isTauriEnv, safeInvoke } from '../utils/tauri'

// ✅ 正确的方式
const result = await safeInvoke('command_name', {}, fallbackValue)

// ❌ 错误的方式
const result = await invoke('command_name')
```

### 2. **模拟数据更新**
如需修改模拟数据，编辑 `src/utils/tauri.js` 中的 `mockData` 对象。

### 3. **新增API调用**
添加新的Tauri API调用时，记得：
- 使用 `safeInvoke` 包装
- 提供合适的fallback值
- 在模拟数据中添加对应的数据结构

## 🎯 调试技巧

### 1. **样式调试**
- 直接在浏览器中使用开发者工具
- 所有页面都可以直接访问
- 实时热重载支持

### 2. **功能调试**
- 查看控制台输出的环境信息
- 使用模拟数据测试各种状态
- 通过 `goldfishDevTools` 手动触发API调用

### 3. **状态调试**
- 应用状态在开发环境下默认为"已激活"
- 可以手动修改 `mockData` 来测试不同状态

## 🔄 生产环境
在生产环境（Tauri打包后）：
- 权限校验正常工作
- 使用真实的Tauri API
- 不会加载模拟数据
- 开发工具不会暴露

## 📝 添加新功能
当添加新的页面或功能时：
1. 确保使用 `safeInvoke` 等安全包装
2. 在 `mockData` 中添加对应的模拟数据
3. 测试在浏览器和Tauri环境下都能正常工作

# Goldfish - 闲鱼商品监控工具

一个基于 Tauri + Vue.js 开发的闲鱼商品监控应用，支持关键词监控、价格筛选和钉钉通知。

## 功能特性

- 🔍 **智能监控**: 支持多关键词、价格区间筛选
- 🔔 **实时通知**: 钉钉机器人推送新商品信息
- 🔐 **安全激活**: RSA加密激活码验证
- 🌐 **网络拦截**: 内置代理服务器，自动获取登录状态
- 🛡️ **安全防护**: 多重安全检测，防止逆向分析
- 💻 **跨平台**: 支持 Windows、macOS、Linux

## 系统架构

```mermaid
graph TB
    subgraph "前端层 (Vue.js 3)"
        UI[用户界面]
        Router[路由管理]
        State[状态管理]
        Components[组件系统]
    end

    subgraph "Tauri Bridge"
        Commands[命令接口]
        Events[事件系统]
        IPC[进程间通信]
    end

    subgraph "后端核心 (Rust)"
        AppController[应用控制器]
        StateManager[状态管理器]

        subgraph "服务层"
            MonitorService[监控服务]
            ProxyService[代理服务]
            BrowserService[浏览器服务]
            ConfigService[配置服务]
            LogService[日志服务]
            DingtalkService[钉钉服务]
            CertificateService[证书服务]
            CertValidationService[证书验证服务]
            DeviceService[设备服务]
            CryptoService[加密服务]
            RsaActivationService[RSA激活服务]
        end

        subgraph "安全模块"
            AntiDebug[反调试]
            AntiTamper[防篡改]
            TimeProtection[时间保护]
            NetworkProtection[网络保护]
            RsaCrypto[RSA加密]
        end

        subgraph "核心功能"
            CertManager[证书管理]
            GoldfishCore[闲鱼核心]
            Utils[工具函数]
        end
    end

    subgraph "外部服务"
        XianyuAPI[闲鱼API]
        DingtalkAPI[钉钉API]
        CRLService[CRL服务:9002]
        ProxyServer[代理服务器:9000]
    end

    subgraph "数据存储"
        ConfigFiles[配置文件]
        LogFiles[日志文件]
        Certificates[证书文件]
        CookieStore[Cookie存储]
    end

    %% 连接关系
    UI --> Commands
    Commands --> AppController
    AppController --> StateManager
    AppController --> MonitorService
    AppController --> ProxyService
    AppController --> BrowserService

    MonitorService --> XianyuAPI
    MonitorService --> DingtalkService
    DingtalkService --> DingtalkAPI

    ProxyService --> ProxyServer
    ProxyService --> CertificateService
    CertificateService --> CRLService

    BrowserService --> ProxyService
    ConfigService --> ConfigFiles
    LogService --> LogFiles

    %% 安全模块连接
    AppController --> AntiDebug
    AppController --> AntiTamper
    RsaActivationService --> RsaCrypto

    %% 数据流
    MonitorService -.-> CookieStore
    ConfigService -.-> ConfigFiles
    CertManager -.-> Certificates

    style UI fill:#e1f5fe
    style MonitorService fill:#f3e5f5
    style ProxyService fill:#e8f5e8
    style BrowserService fill:#fff3e0
    style CertificateService fill:#fce4ec
    style AntiDebug fill:#ffebee
    style XianyuAPI fill:#f1f8e9
    style DingtalkAPI fill:#e3f2fd
```

## 功能模块详解

### 🔍 监控系统
- **关键词监控**: 支持多关键词、价格区间筛选
- **实时数据**: 自动获取闲鱼商品信息
- **智能去重**: 防止重复推送相同商品
- **数据池管理**: 内存优化和定期清理

### 🌐 网络代理
- **MITM代理**: 内置HTTP/HTTPS代理服务器
- **证书管理**: 自动生成和管理CA证书
- **流量拦截**: 拦截和分析网络请求
- **Cookie管理**: 自动获取和维护登录状态

### 🔔 通知系统
- **钉钉推送**: 支持多webhook轮流推送
- **消息限流**: 每分钟20条消息限制
- **模板消息**: ActionCard格式消息
- **推送统计**: 实时监控推送状态

### 🔐 安全防护
- **激活验证**: RSA非对称加密激活码
- **设备绑定**: 硬件指纹识别
- **反调试**: 检测调试器和虚拟机
- **防篡改**: 文件完整性检测
- **时间保护**: 防止系统时间篡改

### 🖥️ 浏览器集成
- **自动登录**: 集成浏览器窗口
- **代理配置**: 自动配置代理设置
- **状态监控**: 实时监控登录状态
- **窗口管理**: 多窗口管理和控制

## 数据流架构

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 前端界面
    participant Monitor as 监控服务
    participant Proxy as 代理服务
    participant Browser as 浏览器服务
    participant Xianyu as 闲鱼API
    participant Dingtalk as 钉钉API

    User->>UI: 启动监控
    UI->>Monitor: 开始监控命令
    Monitor->>Browser: 打开登录窗口
    Browser->>Proxy: 启动代理服务
    Proxy-->>Browser: 代理就绪
    Browser->>User: 显示登录页面

    User->>Browser: 手动登录
    Browser->>Proxy: 拦截登录请求
    Proxy->>Monitor: 提取Cookie
    Monitor->>Monitor: 保存登录状态

    loop 监控循环
        Monitor->>Xianyu: 搜索商品请求
        Xianyu-->>Monitor: 返回商品数据
        Monitor->>Monitor: 数据去重和筛选

        alt 发现新商品
            Monitor->>Dingtalk: 推送通知
            Dingtalk-->>Monitor: 推送确认
            Monitor->>UI: 更新界面状态
        end

        Monitor->>Monitor: 等待间隔时间
    end
```

## 服务架构设计

```mermaid
classDiagram
    class AppController {
        +StateManager state_manager
        +ConfigService config_service
        +MonitorService monitor_service
        +ProxyService proxy_service
        +LogService log_service
        +start_monitoring()
        +stop_monitoring()
        +get_status()
    }

    class MonitorService {
        +LogService log_service
        +BrowserService browser_service
        +DingtalkService dingtalk_service
        +start_monitor()
        +stop_monitor()
        +process_search_results()
    }

    class ProxyService {
        +LogService log_service
        +ConfigService config_service
        +CertificateValidationService cert_validation
        +start_proxy()
        +stop_proxy()
        +handle_request()
    }

    class BrowserService {
        +LogService log_service
        +ProxyService proxy_service
        +open_browser()
        +close_all_browsers()
        +get_active_browsers()
    }

    class CertificateService {
        +LogService log_service
        +check_certificate_trust()
        +validate_certificates()
    }

    class SecurityModule {
        +AntiDebugService anti_debug
        +AntiTamperService anti_tamper
        +TimeProtectionService time_protection
        +RsaCryptoService rsa_crypto
        +perform_security_check()
    }

    AppController --> MonitorService
    AppController --> ProxyService
    AppController --> BrowserService
    MonitorService --> BrowserService
    ProxyService --> CertificateService
    BrowserService --> ProxyService
    AppController --> SecurityModule
```

## 网络拓扑架构

```mermaid
graph LR
    subgraph "本地环境"
        App[Goldfish应用]
        Proxy[代理服务器:9000]
        CRL[CRL服务:9002]
        Browser[内置浏览器]
        Certs[证书存储]
    end

    subgraph "外部网络"
        Xianyu[闲鱼服务器]
        Dingtalk[钉钉服务器]
        CDN[静态资源CDN]
    end

    subgraph "系统集成"
        SystemCerts[系统证书存储]
        NetworkStack[网络协议栈]
        FileSystem[文件系统]
    end

    App --> Proxy
    App --> CRL
    Browser --> Proxy
    Proxy --> Xianyu
    Proxy --> CDN
    App --> Dingtalk

    Proxy -.-> Certs
    Certs -.-> SystemCerts
    CRL -.-> SystemCerts

    Proxy -.-> NetworkStack
    App -.-> FileSystem

    style App fill:#e3f2fd
    style Proxy fill:#e8f5e8
    style CRL fill:#fff3e0
    style Xianyu fill:#ffebee
    style Dingtalk fill:#f3e5f5
```

## 部署架构

```mermaid
flowchart TD
    subgraph "开发环境"
        Dev[开发机器]
        DevBuild[开发构建]
        DevTest[本地测试]
    end

    subgraph "构建系统"
        GitHub[GitHub仓库]
        Actions[GitHub Actions]

        subgraph "多平台构建"
            MacBuild[macOS构建]
            WinBuild[Windows构建]
            LinuxBuild[Linux构建]
        end
    end

    subgraph "分发系统"
        Releases[GitHub Releases]

        subgraph "安装包"
            DMG[macOS .dmg]
            MSI[Windows .msi]
            AppImage[Linux AppImage]
        end
    end

    subgraph "用户环境"
        MacUser[macOS用户]
        WinUser[Windows用户]
        LinuxUser[Linux用户]
    end

    Dev --> GitHub
    GitHub --> Actions
    Actions --> MacBuild
    Actions --> WinBuild
    Actions --> LinuxBuild

    MacBuild --> DMG
    WinBuild --> MSI
    LinuxBuild --> AppImage

    DMG --> Releases
    MSI --> Releases
    AppImage --> Releases

    Releases --> MacUser
    Releases --> WinUser
    Releases --> LinuxUser

    style Dev fill:#e1f5fe
    style Actions fill:#f3e5f5
    style Releases fill:#e8f5e8
```

## 技术特性

### 🏗️ 架构设计
- **服务化架构**: 模块化设计，职责分离
- **事件驱动**: 基于Tauri事件系统的异步通信
- **状态管理**: 统一的应用状态管理
- **依赖注入**: 服务间松耦合设计

### 🔧 技术栈
- **前端**: Vue.js 3 + Naive UI + Vue Router + Vite
- **后端**: Rust + Tauri v2 + Tokio异步运行时
- **网络**: hudsucker MITM代理 + reqwest HTTP客户端
- **加密**: RSA-2048 + SHA256 + 设备指纹
- **存储**: JSON配置文件 + 本地日志系统
- **构建**: Cargo + GitHub Actions CI/CD

### ⚡ 性能优化
- **内存管理**: 智能数据池和定期清理
- **并发处理**: Tokio异步任务调度
- **网络优化**: 连接复用和超时控制
- **资源管理**: 自动资源清理和回收

### 🛡️ 安全机制
- **多层防护**: 反调试 + 防篡改 + 时间保护
- **加密通信**: RSA非对称加密激活验证
- **设备绑定**: 硬件指纹 + MAC地址绑定
- **证书管理**: 自签名CA + CRL吊销列表

## 开发环境要求

- Node.js 16+
- Rust 1.70+
- Tauri CLI v2

## 快速开始

### 安装依赖

```bash
# 安装前端依赖
npm install

# 安装 Tauri CLI
npm install --save-dev @tauri-apps/cli
```

### 开发模式

```bash
# 启动开发服务器
npm run tauri dev
```

### 构建应用

```bash
# 构建生产版本
npm run tauri build
```

## 构建说明

### macOS 构建
- 需要 Xcode 和完整的开发工具链
- 详见 `MACOS_BUILD.md`

### Windows 构建
- 支持原生构建和 Docker 交叉编译
- 详见 `WINDOWS_BUILD.md`

## 安全说明

本应用包含多重安全防护机制，详见 `SECURITY_WARNING_IMPLEMENTATION.md`

## 许可证

本项目仅供学习和研究使用。

## 注意事项

- 请遵守闲鱼平台的使用条款
- 合理使用监控功能，避免对服务器造成过大负载
- 保护好你的激活码和配置信息 